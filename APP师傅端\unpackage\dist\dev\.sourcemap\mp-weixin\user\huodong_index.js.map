{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_index.vue?9699", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_index.vue?daae", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_index.vue?e2cc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_index.vue?ccaa", "uni-app:///user/huodong_index.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_index.vue?6bcc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_index.vue?a22a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "imgUrl", "infos", "inviteCode", "pricedata", "info", "created", "withShareTicket", "menus", "success", "console", "fail", "onShareAppMessage", "title", "path", "imageUrl", "methods", "getImg", "uni", "icon", "copyInviteCode", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoBh3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAX;MACAY;MACAC;MACAC;QACAC;MACA;MACAC;QACAD;MACA;IACA;EAEA;EACAE;IACA;IACA;IACAF;IACA;MACAG;MACAC;MACAC;IACA;IACAL;IACA;EACA;EACAM;IAGAC;MAAA;MACA;QACAP;QACA;QACA;QACA;MACA;QACAA;QACAQ;UACAL;UACAM;QACA;MACA;IACA;IAEAC;MACA;QACAF;UACAL;UACAM;QACA;QACA;MACA;MACAD;QACAlB;QACAS;UACAS;YACAL;YACAM;UACA;QACA;QACAR;UACAD;UACAQ;YACAL;YACAM;UACA;QACA;MACA;IACA;EACA;EACAE;IACAX;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxGA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/huodong_index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/huodong_index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./huodong_index.vue?vue&type=template&id=6e051723&scoped=true&\"\nvar renderjs\nimport script from \"./huodong_index.vue?vue&type=script&lang=js&\"\nexport * from \"./huodong_index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./huodong_index.vue?vue&type=style&index=0&id=6e051723&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6e051723\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/huodong_index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_index.vue?vue&type=template&id=6e051723&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view style=\"color: #E7130E;\" class=\"header\">限时活动</view>\n\t\t<view class=\"box\">\n\t\t\t<view class=\"name\">{{info.name || ''}}</view>\n\t\t\t<view class=\"desc\">今师傅限时特惠，空调清洗仅{{pricedata}}元，快来预约吧！杀菌除螨、去污除异味，给家人一份清新的呼吸！</view>\n\t\t\t<image :src=\"imgUrl\" mode=\"\"></image>\n\t\t\t<!-- <image src=\"http://*************:80/group1/M00/00/0B/CgAQCmhWKQaAQXsoAAAgedw8pn4344.jpg\" mode=\"\"></image> -->\n\t\t\t<view class=\"invite-code-container\">\n\t\t\t\t<!-- <view class=\"invite-code\">邀请码: {{ inviteCode|| '无'}}</view> -->\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"button-container\">\n\t\t<!-- \t<view class=\"btn save-btn\" @click=\"saveImageWithPermission\">保存图片</view> -->\n\t\t\t<button class=\"btn share-btn\" open-type=\"share\" :disabled=\"!imgUrl\">分享</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\timgUrl: '',\n\t\t\tinfos:'',\n\t\t\tinviteCode:'',\r\n\t\t\tpricedata:'',\n\t\t\tinfo: {}\n\t\t}\n\t},\n\tcreated() {\n\t\t//#ifdef MP-WEIXIN\n\t\twx.showShareMenu({\n\t\t\twithShareTicket: true,\n\t\t\tmenus: ['shareAppMessage', 'shareTimeline'],\n\t\t\tsuccess: () => {\n\t\t\t\tconsole.log('Share menu enabled');\n\t\t\t},\n\t\t\tfail: (e) => {\n\t\t\t\tconsole.error('Failed to enable share menu:', e);\n\t\t\t}\n\t\t});\n\t\t//#endif\n\t},\n\tonShareAppMessage(res) {\n\t\tconst inviteCode = this.inviteCode || '';\r\n\t\tconst pricedatas=this.pricedata\r\n\t\tconsole.log(pricedatas)\n\t\tconst shareData = {\n\t\t\ttitle: `今师傅限时特惠，空调清洗仅${pricedatas}元，快来预约吧！杀菌除螨、去污除异味，给家人一份清新的呼吸！`,\n\t\t\tpath: `/pages/service`,\n\t\t\timageUrl: this.imgUrl || ''\n\t\t};\n\t\tconsole.log('Sharing with:', shareData);\n\t\treturn shareData;\n\t},\n\tmethods: {\n\t\n\t\t\n\t\tgetImg() {\n\t\t\tthis.$api.service.huodongselectActivityConfig().then(res => {\n\t\t\t\tconsole.log('QR code fetched:', res);\n\t\t\t\tthis.imgUrl = res.data.sharePictures;\r\n\t\t\t\tthis.pricedata=res.data.payPrice\n\t\t\t\t// this.inviteCode = res.qrCode;\n\t\t\t}).catch(e => {\n\t\t\t\tconsole.error('Failed to fetch QR code:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取二维码失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t\n\t\tcopyInviteCode() {\n\t\t\tif (!this.info.inviteCode) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无邀请码',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: this.info.inviteCode,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '复制成功',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: (e) => {\n\t\t\t\t\tconsole.error('Copy failed:', e);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '复制失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t},\n\tonLoad() {\n\t\tconsole.log('User info loaded:', this.info);\n\t\tthis.getImg();\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tbackground: #f8f8f8;\n\t\theight: 100vh;\n\t\tpadding: 40rpx 30rpx;\n\n\t\t.header {\n\t\t\ttext-align: center;\n\t\t\tfont-size: 52rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #000000;\n\t\t}\n\n\t\t.box {\n\t\t\tmargin-top: 40rpx;\n\t\t\twidth: 690rpx;\n\t\t\theight: 748rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 32rpx;\n\t\t\tpadding: 40rpx 0;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\n\t\t\t.name {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #000000;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\n\t\t\t.desc {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #000000;\n\t\t\t\tpadding: 0 30rpx;\n\t\t\t}\n\n\t\t\timage {\n\t\t\t\twidth: 444rpx;\n\t\t\t\theight: 444rpx;\n\t\t\t\tmargin: 18rpx auto 0;\n\t\t\t}\n\n\t\t\t.invite-code-container {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tgap: 20rpx;\n\t\t\t}\n\n\t\t\t.invite-code {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #000000;\n\t\t\t}\n\n\t\t\t.copy-btn {\n\t\t\t\twidth: 120rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tline-height: 60rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t}\n\t\t}\n\n\t\t.button-container {\n\t\t\tdisplay: flex;\n\t\t\tgap: 20rpx;\n\t\t\tposition: absolute;\n\t\t\tbottom: 42rpx;\n\t\t\twidth: 690rpx;\n\t\t}\n\n\t\t.btn {\n\t\t\tflex: 1;\n\t\t\theight: 98rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 50rpx;\n\t\t\tline-height: 98rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #FFFFFF;\n\t\t\tcursor: pointer;\n\t\t\ttransition: all 0.3s ease;\n\t\t\t\n\t\t\t&:active {\n\t\t\t\tbackground: #1E70EE;\n\t\t\t\ttransform: scale(0.98);\n\t\t\t}\n\t\t}\n\n\t\t.save-btn {\n\t\t\t/* Specific styles for save button if needed */\n\t\t}\n\n\t\t.share-btn {\n\t\t\t/* Ensure button inherits same styles */\n\t\t\tborder: none;\n\t\t\tpadding: 0;\n\t\t\tmargin: 0;\n\t\t\tbackground: #2E80FE;\n\t\t\t/* Remove default button styles */\n\t\t\t&:after {\n\t\t\t\tborder: none;\n\t\t\t}\n\t\t}\n\n\t\t.share-btn[disabled] {\n\t\t\tbackground: #cccccc;\n\t\t\tpointer-events: none;\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_index.vue?vue&type=style&index=0&id=6e051723&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_index.vue?vue&type=style&index=0&id=6e051723&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755740893446\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}