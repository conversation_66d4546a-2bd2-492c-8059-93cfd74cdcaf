<view class="page data-v-261f4edf"><u-modal vue-id="c469e3f2-1" show="{{show}}" title="取消订单" content="确认取消该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e0']]],['^confirm',[['confirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-261f4edf" bind:__l="__l"></u-modal><u-modal vue-id="c469e3f2-2" show="{{afterSalesShow}}" title="申请售后" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e1']]],['^confirm',[['submitAfterSales']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-261f4edf" bind:__l="__l" vue-slots="{{['default']}}"><view class="after-sales-input data-v-261f4edf"><view class="data-v-261f4edf"><textarea style="padding:20rpx;border:2rpx solid #E9E9E9;border-radius:8rpx;writing-mode:horizontal-tb;text-align:left;" placeholder="请输入售后内容" data-event-opts="{{[['input',[['__set_model',['','afterSalesValue','$event',[]]]]]]}}" value="{{afterSalesValue}}" bindinput="__e" class="data-v-261f4edf"></textarea></view></view></u-modal><block wx:if="{{info.coachInfo}}"><view class="header data-v-261f4edf"><view class="top data-v-261f4edf"><view class="left data-v-261f4edf"><view style="display:flex;align-items:center;" class="data-v-261f4edf"><view class="name data-v-261f4edf">{{info.coachInfo.coachName}}</view><block wx:if="{{info.coachInfo.label_name}}"><view style="background-color:#fac21f;color:#fff;width:fit-content;padding:5rpx 10rpx;font-size:24rpx;margin-left:20rpx;border-radius:6rpx;" class="data-v-261f4edf">{{info.coachInfo.label_name}}</view></block></view><view class="time data-v-261f4edf">{{info.createTime}}</view></view><view class="right data-v-261f4edf"><image src="{{info.coachInfo.selfImg}}" mode class="data-v-261f4edf"></image></view></view><block wx:if="{{!$root.g0}}"><view data-event-opts="{{[['tap',[['call',['$event']]]]]}}" class="bott data-v-261f4edf" bindtap="__e"><view class="box data-v-261f4edf"><uni-icons vue-id="c469e3f2-3" type="phone-filled" size="16" color="#fff" class="data-v-261f4edf" bind:__l="__l"></uni-icons></view><text class="data-v-261f4edf">打电话给师傅</text></view></block></view></block><block wx:if="{{info.payType===7}}"><view data-event-opts="{{[['tap',[['openAfterSales',['$event']]]]]}}" class="after-sales-btn data-v-261f4edf" bindtap="__e">去售后</view></block><block wx:if="{{info.payType===7&&info.warrantyType===1}}"><view data-event-opts="{{[['tap',[['viewWarranty',['$event']]]]]}}" class="after-sales-btn data-v-261f4edf" bindtap="__e">查看质保单</view></block><block wx:else><block wx:if="{{info.payType===7}}"><view data-event-opts="{{[['tap',[['opengenerate',['$event']]]]]}}" class="after-sales-btn data-v-261f4edf" bindtap="__e">生成质保单</view></block></block><block wx:if="{{info.payType!==7}}"><view class="schedule data-v-261f4edf"><u-steps vue-id="c469e3f2-4" current="4" direction="column" class="data-v-261f4edf" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-steps-item vue-id="{{('c469e3f2-5-'+index)+','+('c469e3f2-4')}}" title="{{item.title}}" desc="{{item.desc}}" class="data-v-261f4edf" bind:__l="__l" vue-slots="{{['icon']}}"><view class="slot-icon data-v-261f4edf" slot="icon"><view style="border-radius:50%;background-color:#00b26a;padding:5rpx;" class="data-v-261f4edf"><u-icon vue-id="{{('c469e3f2-6-'+index)+','+('c469e3f2-5-'+index)}}" name="checkbox-mark" color="#ffffff" size="14" class="data-v-261f4edf" bind:__l="__l"></u-icon></view></view></u-steps-item></block></u-steps></view></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info data-v-261f4edf"><view class="title data-v-261f4edf">{{item.$orig.title}}</view><block wx:if="{{item.$orig.title==='服务信息'}}"><view class="service-info data-v-261f4edf"><block wx:for="{{item.l0}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view class="info_item data-v-261f4edf"><view class="left data-v-261f4edf">{{newItem.name}}</view><view class="right data-v-261f4edf">{{newItem.value}}</view></view></block><view class="goods-list data-v-261f4edf"><block wx:for="{{info.orderGoods}}" wx:for-item="goods" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods-card data-v-261f4edf"><view class="goods-main data-v-261f4edf"><image class="goods-image data-v-261f4edf" src="{{goods.goodsCover}}" mode="aspectFill"></image><view class="goods-content data-v-261f4edf"><view class="goods-name data-v-261f4edf">{{goods.goodsName}}</view><view class="goods-details data-v-261f4edf"><block wx:for="{{goods.priceSetting}}" wx:for-item="setting" wx:for-index="__i0__" wx:key="id"><view class="detail-item data-v-261f4edf"><block wx:if="{{setting.val&&setting.val!==''&&setting.inputType!==2}}"><text class="detail-text data-v-261f4edf">{{setting.problemDesc+"："+setting.val}}</text></block></view></block></view></view><view class="goods-right data-v-261f4edf"><block wx:if="{{goods.price>0}}"><view class="goods-price data-v-261f4edf">{{"¥"+goods.price}}</view></block><view class="goods-num data-v-261f4edf">{{"x"+goods.num}}</view></view></view></view></block></view></view></block><block wx:else><block wx:for="{{item.$orig.children}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view class="info_item data-v-261f4edf"><view class="left data-v-261f4edf">{{newItem.name}}</view><view class="right data-v-261f4edf">{{newItem.value}}</view></view></block></block></view></block><block wx:if="{{info.payType<=1&&info.payType!=-1}}"><view data-event-opts="{{[['tap',[['cancelOrder',['$event']]]]]}}" class="btn data-v-261f4edf" bindtap="__e">取消订单</view></block></view>