<view class="page-container data-v-893dab3e"><view class="header data-v-893dab3e"><view class="header-title data-v-893dab3e">师傅信用信息</view></view><block wx:if="{{loading}}"><view class="loading-container data-v-893dab3e"><view class="loading-text data-v-893dab3e">加载中...</view></view></block><block wx:else><view class="credit-content data-v-893dab3e"><view class="credit-card data-v-893dab3e"><view class="credit-header data-v-893dab3e"><view class="credit-title data-v-893dab3e">当前信用分</view><view class="credit-score data-v-893dab3e">{{currentCredit}}</view></view><view class="credit-level data-v-893dab3e">{{$root.m0}}</view></view><view class="filter-section data-v-893dab3e"><view class="filter-title data-v-893dab3e">筛选条件</view><view class="filter-options data-v-893dab3e"><view class="filter-item data-v-893dab3e"><view class="filter-label data-v-893dab3e">信誉分类型:</view><picker value="{{typeIndex}}" range="{{typeOptions}}" range-key="label" data-event-opts="{{[['change',[['onTypeChange',['$event']]]]]}}" bindchange="__e" class="data-v-893dab3e"><view class="picker-text data-v-893dab3e">{{typeOptions[typeIndex].label}}</view></picker></view><view class="filter-item data-v-893dab3e"><view class="filter-label data-v-893dab3e">变动原因:</view><picker value="{{reasonIndex}}" range="{{reasonOptions}}" range-key="label" data-event-opts="{{[['change',[['onReasonChange',['$event']]]]]}}" bindchange="__e" class="data-v-893dab3e"><view class="picker-text data-v-893dab3e">{{reasonOptions[reasonIndex].label}}</view></picker></view></view><view class="filter-actions data-v-893dab3e"><button data-event-opts="{{[['tap',[['resetFilters',['$event']]]]]}}" class="reset-btn data-v-893dab3e" bindtap="__e">重置</button><button data-event-opts="{{[['tap',[['loadCreditInfo',['$event']]]]]}}" class="search-btn data-v-893dab3e" bindtap="__e">查询</button></view></view><view class="credit-records data-v-893dab3e"><view class="records-title data-v-893dab3e">信用变动记录</view><block wx:if="{{$root.g0===0}}"><view class="no-data data-v-893dab3e"><view class="no-data-text data-v-893dab3e">暂无记录</view></view></block><block wx:else><view class="records-list data-v-893dab3e"><block wx:for="{{$root.l0}}" wx:for-item="record" wx:for-index="index" wx:key="id"><view class="record-item data-v-893dab3e"><view class="record-header data-v-893dab3e"><view class="{{['record-type','data-v-893dab3e',record.$orig.creditType===1?'add':'minus']}}">{{''+(record.$orig.creditType===1?'+':'-')+record.$orig.points+'分'}}</view><view class="record-date data-v-893dab3e">{{record.m1}}</view></view><view class="record-content data-v-893dab3e"><view class="record-reason data-v-893dab3e">{{record.m2}}</view><view class="record-detail data-v-893dab3e"><text class="data-v-893dab3e">{{"变动前: "+record.$orig.beforePoints+"分"}}</text><text class="data-v-893dab3e">{{"变动后: "+record.$orig.afterPoints+"分"}}</text></view><block wx:if="{{record.$orig.remark}}"><view class="record-remark data-v-893dab3e">{{"备注: "+record.$orig.remark}}</view></block></view></view></block></view></block><block wx:if="{{totalCount>0}}"><view class="pagination-info data-v-893dab3e"><text class="data-v-893dab3e">{{"共"+totalCount+"条记录，第"+pageNum+"/"+totalPage+"页"}}</text></view></block><block wx:if="{{hasMore}}"><view data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" class="load-more data-v-893dab3e" bindtap="__e"><view class="load-more-text data-v-893dab3e">{{loadingMore?'加载中...':'加载更多'}}</view></view></block></view></view></block></view>