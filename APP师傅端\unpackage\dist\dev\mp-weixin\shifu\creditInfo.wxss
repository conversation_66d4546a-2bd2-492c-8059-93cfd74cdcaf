@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page-container.data-v-893dab3e {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header.data-v-893dab3e {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  color: white;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.header-title.data-v-893dab3e {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}
.loading-container.data-v-893dab3e {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text.data-v-893dab3e {
  font-size: 28rpx;
  color: #999;
}
.credit-content.data-v-893dab3e {
  padding: 30rpx;
}
.credit-card.data-v-893dab3e {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  color: white;
  box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
}
.credit-header.data-v-893dab3e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.credit-title.data-v-893dab3e {
  font-size: 32rpx;
  font-weight: bold;
}
.credit-score.data-v-893dab3e {
  font-size: 48rpx;
  font-weight: bold;
}
.credit-level.data-v-893dab3e {
  font-size: 28rpx;
  opacity: 0.9;
}
.filter-section.data-v-893dab3e {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.filter-title.data-v-893dab3e {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}
.filter-options.data-v-893dab3e {
  margin-bottom: 30rpx;
}
.filter-item.data-v-893dab3e {
  margin-bottom: 30rpx;
}
.filter-label.data-v-893dab3e {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-weight: bold;
}
.filter-dropdown.data-v-893dab3e {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}
.dropdown-item.data-v-893dab3e {
  font-size: 26rpx;
  color: #666;
  padding: 15rpx 25rpx;
  background: #f8f9fa;
  border-radius: 25rpx;
  border: 2rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
}
.dropdown-item.active.data-v-893dab3e {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.dropdown-item.data-v-893dab3e:hover {
  background: #e9ecef;
}
.dropdown-item.active.data-v-893dab3e:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.filter-actions.data-v-893dab3e {
  display: flex;
  gap: 20rpx;
}
.reset-btn.data-v-893dab3e, .search-btn.data-v-893dab3e {
  flex: 1;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}
.reset-btn.data-v-893dab3e {
  background: #f8f9fa;
  color: #666;
}
.search-btn.data-v-893dab3e {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
.credit-records.data-v-893dab3e {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.records-title.data-v-893dab3e {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}
.no-data.data-v-893dab3e {
  text-align: center;
  padding: 80rpx 0;
}
.no-data-text.data-v-893dab3e {
  font-size: 28rpx;
  color: #999;
}
.records-list .record-item.data-v-893dab3e {
  border-bottom: 1px solid #f0f0f0;
  padding: 20rpx 0;
}
.records-list .record-item.data-v-893dab3e:last-child {
  border-bottom: none;
}
.record-header.data-v-893dab3e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.record-type.data-v-893dab3e {
  font-size: 32rpx;
  font-weight: bold;
}
.record-type.add.data-v-893dab3e {
  color: #52c41a;
}
.record-type.minus.data-v-893dab3e {
  color: #ff4d4f;
}
.record-date.data-v-893dab3e {
  font-size: 24rpx;
  color: #999;
}
.record-content.data-v-893dab3e {
  padding-left: 20rpx;
}
.record-reason.data-v-893dab3e {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.record-detail.data-v-893dab3e {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.record-detail text.data-v-893dab3e {
  margin-right: 20rpx;
}
.record-remark.data-v-893dab3e {
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}
.pagination-info.data-v-893dab3e {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
}
.load-more.data-v-893dab3e {
  text-align: center;
  padding: 30rpx 0;
  cursor: pointer;
}
.load-more-text.data-v-893dab3e {
  font-size: 28rpx;
  color: #667eea;
}

