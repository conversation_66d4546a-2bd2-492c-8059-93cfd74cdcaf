{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_order.vue?abdc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_order.vue?f977", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_order.vue?a58b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_order.vue?9340", "uni-app:///user/huodong_order.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_order.vue?4622", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/huodong_order.vue?6a60"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "couType", "confirmCou", "type", "id", "tmplIds", "pid", "notYh", "showYh", "value", "showEx", "itemArr", "newItemArr", "needShow", "notes", "showChoose", "content", "currentDate", "timeArr", "disabled", "time", "time1", "time2", "currentTime", "conDate", "conTime", "dateArr", "week", "m<PERSON><PERSON><PERSON><PERSON>", "huodongdata", "couponlist", "nocouponlist", "couponNum", "form", "isSubmitting", "computed", "allMoney", "num", "methods", "goUrl", "console", "uni", "url", "success", "fail", "icon", "title", "duration", "addcar", "serviceId", "setTimeout", "submit", "addressId", "startTime", "endTime", "text", "couponId", "dingyue", "chooseNotyh", "item", "chooseItemyh", "e", "chooseYh", "expandAll", "confirmTime", "addLeadingZero", "tapTime", "tapDate", "updateTimeAvailability", "getTime", "str", "date", "fullDate", "getdefultaddress", "res", "getservice", "gethuodongconfig", "getList", "status", "arr", "arr1", "onLoad", "onShow", "that", "onHide", "onUnload", "watch", "handler", "immediate"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2Gh3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,UACA,gDACA,+CACA,+CACA,8CACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,UACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAC;QACAC;UACAH;QACA;QACAI;UACAJ;UACAC;YACAI;YACAC;YACAC;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;MACA;QACAC;QACAZ;MACA;QACAI;UACAI;UACAC;QACA;QACAI;UACA;UACAT;YACAC;UACA;QACA;MACA;QACA;QACAD;UACAI;UACAC;UACAC;QACA;QACAP;MACA;IACA;IACAW;MAAA;MACA;QACA;MACA;MACA;QACAV;UACAI;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QACA5C;QACAG;QACA8C;QACAH;QACAI;QACAC;QACAjB;QACAkB;QACAC;MACA;MACAhB;MAEA;QACA;UACAC;YACAI;YACAC;YACAC;UACA;UACAG;YACA;YACAT;cACAC;YACA;UACA;QACA;MACA;QACA;QACAD;UACAI;UACAC;UACAC;QACA;QACAP;MACA;IACA;IAEAiB;MACA;MACA;QACAjB;QACA;MACA;MACA;QAAA;MAAA;MACA;MAEAC;QACApC;QACAsC;UACAH;QACA;QACAI;UACAJ;QACA;MACA;IAEA;IACAkB;MACA;MACA;MACA;QACA;UACAC;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;UACA;YACAnB;cACAI;cACAC;YACA;YACA;UACA;QACA;QACA;UACAe;QACA;QACAF;QACA;QACA;QACA;MACA;QACAA;QACA;QACA;MACA;IACA;IACAG;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;QACAvB;UACAI;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QACAN;UACAI;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IACAkB;MACA;IACA;IACAC;MACA;QACAzB;UACAI;UACAC;UACAC;QACA;QACA;MACA;MACA;IACA;IACAoB;MACA;MACA;MACA;MACA;IACA;IACAC;MACA5B;MACA;QACA;QACA;QACA;QACA;UACA;YACAA;YACAmB;YACA;UACA;UACA;UACA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;QACA;UACA;YACAA;UACA;QACA;MACA;MACAnB;IACA;IACA6B;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACAC;UACAC;UACAC;QACA;QACAvD;MACA;MACA;IACA;IACAwD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAmC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAD;gBACAlC;gBACAkC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAoC;MAAA;MACA;QACA;UACA;UACA;UACApC;QACA;QACAA;MACA;IACA;IACAqC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OACA;cAAA;gBAAAJ;gBACAlC;gBACAA;gBACAuC;gBACAC;gBACAN;kBACAf;kBACA;oBACAA;sBACA;wBACAoB;sBACA;wBACAC;sBACA;oBACA;kBACA;oBACAA;kBACA;gBACA;gBACAD;gBACAC;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACAyC;IACA;IACAzC;IACAA;IACA;IACA;IACAA;IACA;IACA;IACA;IACA;EACA;EACA0C;IACA;IACA;MACA;MACA;MACA;IACA;MACA;IACA;IACAzC;MACA0C;IACA;EACA;EACAC,2BACA;EACAC;IACA;EACA;EACAC;IACArE;MACAsE;QACA;MACA;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9gBA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/huodong_order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/huodong_order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./huodong_order.vue?vue&type=template&id=f26050c2&scoped=true&\"\nvar renderjs\nimport script from \"./huodong_order.vue?vue&type=script&lang=js&\"\nexport * from \"./huodong_order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./huodong_order.vue?vue&type=style&index=0&id=f26050c2&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f26050c2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/huodong_order.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_order.vue?vue&type=template&id=f26050c2&scoped=true&\"", "var components\ntry {\n  components = {\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.couponlist.length == 0 && _vm.nocouponlist.length == 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showYh = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_order.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<u-modal :show=\"showChoose || showYh\" :content='content'></u-modal>\n\n\t\t<!-- 优惠券弹出框 -->\n\t\t<view class=\"choose_yh\" :style=\"showYh?'':'height:0'\">\n\t\t\t<view class=\"head\">优惠券</view>\n\t\t\t<view class=\"close\" @click=\"showYh = false\">\n\t\t\t\t<image src=\"../static/images/9397.png\" mode=\"\"></image>\n\t\t\t</view>\n\t\t\t<u-empty mode=\"coupon\" icon=\"http://cdn.uviewui.com/uview/empty/coupon.png\" v-if=\"couponlist.length == 0 && nocouponlist.length == 0\">\n\t\t\t</u-empty>\n\t\t\t<scroll-view scroll-y=\"true\" style=\"height: 832rpx;\" v-else>\n\t\t\t\t<view class=\"cou_item\" v-for=\"(item,index) in couponlist\" :key=\"index\">\n\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t<view class=\"box1\" v-if=\"item.type == 0\">\n\t\t\t\t\t\t\t<span>满</span>{{item.full}}<span>减</span>{{item.discount}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"box1\" v-else><span>￥</span>{{item.discount}}</view>\n\t\t\t\t\t\t<view class=\"box2\">\n\t\t\t\t\t\t\t<text>{{item.title}}</text>\n\t\t\t\t\t\t\t<span v-if=\"item.start_time == 0\">有效期：自领券日起{{item.day}}天</span>\n\t\t\t\t\t\t\t<span v-else>有效期：{{item.start_time}}</span>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"box3\" :style=\"item.choose?'background:#2E80FE;border:2rpx solid #2E80FE':''\"\n\t\t\t\t\t\t\t@click=\"chooseItemyh(item)\">\n\t\t\t\t\t\t\t<u-icon name=\"checkbox-mark\" color=\"#fff\" size=\"16\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t\t{{item.rule}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"noYh\">\n\t\t\t\t\t<view class=\"left\">不使用优惠券</view>\n\t\t\t\t\t<view class=\"right\" :style=\"notYh?'background:#2E80FE;border:2rpx solid #2E80FE':''\"\n\t\t\t\t\t\t@click=\"chooseNotyh()\"><u-icon name=\"checkbox-mark\" color=\"#fff\" size=\"16\"></u-icon></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"notcan\">\n\t\t\t\t\t不可使用优惠券\n\t\t\t\t</view>\n\t\t\t\t<view class=\"cou_item\" style=\"border: 2rpx solid #ADADAD;background: #fff;\"\n\t\t\t\t\tv-for=\"(item,index) in nocouponlist\" :key=\"index\">\n\t\t\t\t\t<view class=\"top\" style=\"border-bottom: 2rpx dashed #ADADAD;\">\n\t\t\t\t\t\t<view class=\"box1\" v-if=\"item.type == 0\" style=\"color: #ADADAD ;\">\n\t\t\t\t\t\t\t<span>满</span>{{item.full}}<span>减</span>{{item.discount}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"box1\" style=\"color: #ADADAD ;\"><span>￥</span>{{item.discount}}</view>\n\t\t\t\t\t\t<view class=\"box2\">\n\t\t\t\t\t\t\t<text style=\"color: #ADADAD ;\">{{item.title}}</text>\n\t\t\t\t\t\t\t<span v-if=\"item.start_time == 0\">有效期：自领券日起{{item.day}}天</span>\n\t\t\t\t\t\t\t<span v-else>生效时间：{{item.start_time}}</span>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t\t{{item.rule}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\n\t\t<view class=\"address\" @click=\"goUrl\">\n\t\t\t<view class=\"left\">\n\t\t\t\t<view class=\"top\">\n\t\t\t\t\t<image src=\"../static/images/position.png\" mode=\"\"></image>\n\t\t\t\t\t<text style=\"color: #599eff;\">{{mrAddress.address?mrAddress.address:'请先添加地址哦'}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bottom\">{{mrAddress.address?mrAddress.userName+mrAddress.mobile:''}}</view>\n\t\t\t</view>\n\t\t\t<u-icon name=\"arrow-right\" color=\"#333333\" size=\"14\"></u-icon>\n\t\t</view>\n\t\t<view class=\"fg\"></view>\n\t\t<view class=\"main\">\n\t\t\t<view class=\"main_item\" v-for=\"(item,index) in newItemArr\" :key=\"index\">\n\t\t\t\t<image :src=\"item.cover\" mode=\"\"></image>\n\t\t\t\t<view class=\"right\">\n\t\t\t\t\t<view class=\"title\">{{item.title}}</view>\n\t\t\t\t\t<view class=\"price\" v-if=\"type == 0\">\n\t\t\t\t\t\t<text>￥{{huodongdata.payPrice}}/台</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"expand\" @click=\"expandAll\" v-if=\"needShow\">\n\t\t\t\t{{showEx?'展开详情':'收起'}}\n\t\t\t\t<view class=\"icon_box\">\n\t\t\t\t\t<u-icon :name=\"showEx?'arrow-down':'arrow-up'\" color=\"#ADADAD\" size=\"14\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"fg\"></view>\n\t\t<view class=\"notes\">\n\t\t\t<view class=\"title\">服务备注</view>\n\t\t\t<textarea cols=\"25\" rows=\"5\" placeholder=\"想要额外嘱咐工作人员的可以备注哦~\" v-model=\"notes\"></textarea>\n\t\t</view>\n\t\t<view class=\"fg\"></view>\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"left\"></view>\n\t\t\t<view class=\"mid\">\n\t\t\t</view>\n\t\t\t<view class=\"right\" :class=\"{'disabled': isSubmitting}\" @click=\"submit\">\n\t\t\t\t{{isSubmitting ? '提交中...' : '立即下单'}}\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tcouType: false,\n\t\t\tconfirmCou: null,\n\t\t\ttype: '',\n\t\t\tid: '',\n\t\t\ttmplIds: [\n\t\t\t\t' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t'9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY',\n\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t],\n\t\t\tpid: 0,\n\t\t\tnotYh: false,\n\t\t\tshowYh: false,\n\t\t\tvalue: '',\n\t\t\tshowEx: false,\n\t\t\titemArr: [],\n\t\t\tnewItemArr: [],\n\t\t\tneedShow: false,\n\t\t\tnotes: '',\n\t\t\tshowChoose: false,\n\t\t\tcontent: '',\n\t\t\tcurrentDate: 0,\n\t\t\ttimeArr: [\n\t\t\t\t{ disabled: false, time: '00:00-02:00', time1: '00:00:00', time2: '02:00:00' },\n\t\t\t\t{ disabled: false, time: '02:00-04:00', time1: '02:00:00', time2: '04:00:00' },\n\t\t\t\t{ disabled: false, time: '04:00-06:00', time1: '04:00:00', time2: '06:00:00' },\n\t\t\t\t{ disabled: false, time: '06:00-08:00', time1: '06:00:00', time2: '08:00:00' },\n\t\t\t\t{ disabled: false, time: '08:00-10:00', time1: '08:00:00', time2: '10:00:00' },\n\t\t\t\t{ disabled: false, time: '10:00-12:00', time1: '10:00:00', time2: '12:00:00' },\n\t\t\t\t{ disabled: false, time: '12:00-14:00', time1: '12:00:00', time2: '14:00:00' },\n\t\t\t\t{ disabled: false, time: '14:00-16:00', time1: '14:00:00', time2: '16:00:00' },\n\t\t\t\t{ disabled: false, time: '16:00-18:00', time1: '16:00:00', time2: '18:00:00' },\n\t\t\t\t{ disabled: false, time: '18:00-20:00', time1: '18:00:00', time2: '20:00:00' },\n\t\t\t\t{ disabled: false, time: '20:00-22:00', time1: '20:00:00', time2: '22:00:00' },\n\t\t\t\t{ disabled: false, time: '22:00-24:00', time1: '22:00:00', time2: '24:00:00' }\n\t\t\t],\n\t\t\tcurrentTime: -1,\n\t\t\tconDate: '选择可上门时间',\n\t\t\tconTime: '',\n\t\t\tdateArr: [],\n\t\t\tweek: [\"周日\", \"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\"],\n\t\t\tmrAddress: {},\n\t\t\thuodongdata: '',\n\t\t\tcouponlist: [],\n\t\t\tnocouponlist: [],\n\t\t\tcouponNum: '',\n\t\t\tform: {},\n\t\t\tisSubmitting: false\n\t\t}\n\t},\n\tcomputed: {\n\t\tallMoney() {\n\t\t\tlet num = 0;\n\t\t\tthis.itemArr.forEach(item => {\n\t\t\t\tnum += item.num * item.price;\n\t\t\t});\n\t\t\tif (this.confirmCou == null) {\n\t\t\t\treturn num;\n\t\t\t} else {\n\t\t\t\treturn (num * 1 - this.confirmCou.discount * 1) < 0 ? 0 : (num * 1 - this.confirmCou.discount * 1);\n\t\t\t}\n\t\t}\n\t},\n\tmethods: {\n\t\tgoUrl() {\n\t\t\tif (this.isSubmitting) return;\n\t\t\tconsole.log('goUrl triggered');\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '../user/huodongaddress',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tconsole.log('Navigation to address page successful');\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('Navigation failed:', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '导航失败，请检查页面路径',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\taddcar() {\n\t\t\tif (this.isSubmitting) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.isSubmitting = true;\n\t\t\tthis.$api.service.addtocar({\n\t\t\t\tserviceId: this.id,\n\t\t\t\tnum: this.newItemArr[0].num\n\t\t\t}).then(res => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\ttitle: '加入成功'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.isSubmitting = false;\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '../user/order'\n\t\t\t\t\t});\n\t\t\t\t}, 1000);\n\t\t\t}).catch(err => {\n\t\t\t\tthis.isSubmitting = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '加入购物车失败，请重试',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\tconsole.error('Add to cart failed:', err);\n\t\t\t});\n\t\t},\n\t\tsubmit() {\n\t\t\tif (this.isSubmitting) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (!this.mrAddress.id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请先选择地址',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.isSubmitting = true;\n\t\t\tlet subForm = {\n\t\t\t\ttype: this.type,\n\t\t\t\tpid: this.pid,\n\t\t\t\taddressId: this.mrAddress.id,\n\t\t\t\tserviceId: this.id,\n\t\t\t\tstartTime: 1,\n\t\t\t\t\t\t\t\tendTime: 1,\n\t\t\t\tnum: this.newItemArr[0].num || 1,\n\t\t\t\ttext: this.notes,\n\t\t\t\tcouponId: this.confirmCou == null ? '' : this.confirmCou.id,\n\t\t\t};\n\t\t\tconsole.log('提交表单数据:', subForm);\n\t\t\t\n\t\t\tthis.$api.service.subHuoOrder(subForm).then(res => {\n\t\t\t\tif(res.code===\"200\"){\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\ttitle: '提交成功',\n\t\t\t\t\t\tduration: 500\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.isSubmitting = false;\n\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\turl: '../user/tiaozhuan'\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 500);\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tthis.isSubmitting = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\tconsole.error('Submit order failed:', err);\n\t\t\t});\n\t\t},\n\t\n\t\tdingyue() {\n\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());\n\t\t\tconst selectedTmplIds = shuffled.slice(0, 3);\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tuni.requestSubscribeMessage({\n\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('requestSubscribeMessage success:', res, 'with tmplIds:', selectedTmplIds);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('requestSubscribeMessage failed:', err, 'with tmplIds:', selectedTmplIds);\n\t\t\t\t}\n\t\t\t});\n\t\t\t// #endif\n\t\t},\n\t\tchooseNotyh() {\n\t\t\tif (this.isSubmitting) return;\n\t\t\tthis.notYh = !this.notYh;\n\t\t\tif (this.notYh) {\n\t\t\t\tthis.couponlist.forEach(item => {\n\t\t\t\t\titem.choose = false;\n\t\t\t\t});\n\t\t\t\tthis.confirmCou = null;\n\t\t\t}\n\t\t},\n\t\tchooseItemyh(item) {\n\t\t\tif (this.isSubmitting) return;\n\t\t\tif (item.choose == false) {\n\t\t\t\tif (item.type == 0) {\n\t\t\t\t\tif (item.full * 1 > this.allMoney * 1) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '当前金额未满足使用条件'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.couponlist.forEach(e => {\n\t\t\t\t\te.choose = false;\n\t\t\t\t});\n\t\t\t\titem.choose = true;\n\t\t\t\tthis.couType = true;\n\t\t\t\tthis.confirmCou = item;\n\t\t\t\tthis.notYh = false;\n\t\t\t} else {\n\t\t\t\titem.choose = false;\n\t\t\t\tthis.couType = false;\n\t\t\t\tthis.confirmCou = null;\n\t\t\t}\n\t\t},\n\t\tchooseYh() {\n\t\t\tif (this.isSubmitting) return;\n\t\t\tthis.showYh = true;\n\t\t},\n\t\texpandAll() {\n\t\t\tif (this.showEx) {\n\t\t\t\tthis.newItemArr = this.itemArr;\n\t\t\t\tthis.showEx = false;\n\t\t\t} else {\n\t\t\t\tthis.newItemArr = this.itemArr.slice(0, 2);\n\t\t\t\tthis.showEx = true;\n\t\t\t}\n\t\t},\n\t\tconfirmTime() {\n\t\t\tif (this.currentTime === -1) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请选择预约时间',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst selectedTime = this.timeArr[this.currentTime];\n\t\t\tif (selectedTime.disabled) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '该时间段不可用',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.conDate = this.dateArr[this.currentDate].date + '(' + this.dateArr[this.currentDate].str + ')';\n\t\t\tthis.conTime = selectedTime.time;\n\t\t\tthis.showChoose = false;\n\t\t},\n\t\taddLeadingZero(number) {\n\t\t\treturn number < 10 ? '0' + number : number;\n\t\t},\n\t\ttapTime(item, index) {\n\t\t\tif (!item || !item.time || item.disabled) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '该时间段不可选择',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.currentTime = index;\n\t\t},\n\t\ttapDate(item, index) {\n\t\t\tthis.currentDate = index;\n\t\t\tthis.currentTime = -1;\n\t\t\tthis.conTime = '';\n\t\t\tthis.updateTimeAvailability(index);\n\t\t},\n\t\tupdateTimeAvailability(dateIndex) {\n\t\t\tconsole.log('Updating time availability for dateIndex:', dateIndex);\n\t\t\tif (dateIndex === 0) {\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst currentHour = now.getHours();\n\t\t\t\tconst currentMinute = now.getMinutes();\n\t\t\t\tthis.timeArr.forEach((item, index) => {\n\t\t\t\t\tif (!item.time1) {\n\t\t\t\t\t\tconsole.warn(`Invalid time slot at index ${index}:`, item);\n\t\t\t\t\t\titem.disabled = true;\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tconst timeStart = parseInt(item.time1.split(':')[0]);\n\t\t\t\t\tconst timeStartMinutes = parseInt(item.time1.split(':')[1]);\n\t\t\t\t\tif (currentHour > timeStart || (currentHour === timeStart && currentMinute >= timeStartMinutes)) {\n\t\t\t\t\t\titem.disabled = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\titem.disabled = false;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tthis.timeArr.forEach(item => {\n\t\t\t\t\tif (item.time1) {\n\t\t\t\t\t\titem.disabled = false;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\tconsole.log('Updated timeArr:', this.timeArr);\n\t\t},\n\t\tgetTime() {\n\t\t\tconst now = new Date();\n\t\t\tlet currentDate = new Date(now);\n\t\t\tfor (let i = 0; i < 4; i++) {\n\t\t\t\tconst month = this.addLeadingZero(currentDate.getMonth() + 1);\n\t\t\t\tconst date = this.addLeadingZero(currentDate.getDate());\n\t\t\t\tconst day = currentDate.getDay();\n\t\t\t\tconst year = currentDate.getFullYear();\n\t\t\t\tthis.dateArr.push({\n\t\t\t\t\tstr: i === 0 ? '今天' : this.week[day],\n\t\t\t\t\tdate: month + '-' + date,\n\t\t\t\t\tfullDate: `${year}-${month}-${date}`\n\t\t\t\t});\n\t\t\t\tcurrentDate.setDate(currentDate.getDate() + 1);\n\t\t\t}\n\t\t\tthis.updateTimeAvailability(0);\n\t\t},\n\t\tasync getdefultaddress() {\n\t\t\ttry {\n\t\t\t\tlet res = await this.$api.mine.getDefultAddress();\n\t\t\t\tthis.mrAddress = res;\n\t\t\t} catch (err) {\n\t\t\t\tconsole.error('Get default address failed:', err);\n\t\t\t}\n\t\t},\n\t\tasync getservice() {\n\t\t\ttry {\n\t\t\t\tlet res = await this.$api.service.serviceInfo(this.id);\n\t\t\t\tconsole.log(res);\n\t\t\t\tres.num = 1;\n\t\t\t\tthis.itemArr.push(res);\n\t\t\t} catch (err) {\n\t\t\t\tconsole.error('Get service info failed:', err);\n\t\t\t}\n\t\t},\n\t\tgethuodongconfig() {\n\t\t\tthis.$api.service.huodongselectActivityConfig().then(res => {\n\t\t\t\tif(res.code===\"200\"){\n\t\t\t\t\tthis.huodongdata = res.data;\n\t\t\t\t\tthis.isPageLoaded = true;\n\t\t\t\t\tconsole.log(this.huodongdata);\n\t\t\t\t}\n\t\t\t\tconsole.log(res);\n\t\t\t});\n\t\t},\n\t\tasync getList() {\n\t\t\ttry {\n\t\t\t\tlet status = 1;\n\t\t\t\tlet res = await this.$api.service.myWelfare(status);\n\t\t\t\tconsole.log(res.data);\n\t\t\t\tconsole.log(res);\n\t\t\t\tlet arr = [];\n\t\t\t\tlet arr1 = [];\n\t\t\t\tres.list.forEach(item => {\n\t\t\t\t\titem.choose = false;\n\t\t\t\t\tif (item.service.length > 0) {\n\t\t\t\t\t\titem.service.forEach(e => {\n\t\t\t\t\t\t\tif (e.id == this.id) {\n\t\t\t\t\t\t\t\tarr.push(item);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tarr1.push(item);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tarr1.push(item);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tarr = [...new Set(arr)];\n\t\t\t\tarr1 = [...new Set(arr1)];\n\t\t\t\tthis.couponlist = arr;\n\t\t\t\tthis.nocouponlist = arr1;\n\t\t\t\tthis.couponNum = arr.length;\n\t\t\t} catch (err) {\n\t\t\t\tconsole.error('Get coupon list failed:', err);\n\t\t\t}\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tthis.type = 0;\n\t\tconsole.log('======', this.type);\n\t\tconsole.log('======', options);\n\t\tthis.id = options.id;\n\t\tthis.pid = uni.getStorageSync('pid');\n\t\tconsole.log(this.pid);\n\t\tthis.getdefultaddress();\n\t\tthis.getservice();\n\t\tthis.getTime();\n\t\tthis.gethuodongconfig();\n\t},\n\tonShow() {\n\t\tlet that = this;\n\t\tif (this.itemArr.length && this.itemArr.length > 2) {\n\t\t\tthis.showEx = true;\n\t\t\tthis.needShow = true;\n\t\t\tthis.newItemArr = this.itemArr.slice(0, 2);\n\t\t} else {\n\t\t\tthis.newItemArr = this.itemArr;\n\t\t}\n\t\tuni.$once('chooseAddress', function(e) {\n\t\t\tthat.mrAddress = e;\n\t\t});\n\t},\n\tonHide() {\n\t},\n\tonUnload() {\n\t\tthis.isSubmitting = false;\n\t},\n\twatch: {\n\t\tcurrentDate: {\n\t\t\thandler(nval) {\n\t\t\t\tthis.updateTimeAvailability(nval);\n\t\t\t},\n\t\t\timmediate: true\n\t\t}\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tpadding-bottom: 200rpx;\n\n\t::v-deep .u-popup__content {\n\t\tdisplay: none;\n\t}\n\n\t::v-deep .u-number-box__plus {\n\t\tborder-radius: 50%;\n\t\twidth: 36rpx;\n\t\theight: 36rpx !important;\n\t\tbackground-color: #fff !important;\n\t\tborder: 1px solid #000;\n\n\t\ttext {\n\t\t\tfont-size: 24rpx !important;\n\t\t\tline-height: 36rpx !important;\n\t\t}\n\t}\n\n\t::v-deep .u-number-box__minus {\n\t\tborder-radius: 50%;\n\t\twidth: 36rpx;\n\t\theight: 36rpx !important;\n\t\tbackground-color: #fff !important;\n\t\tborder: 1px solid #000;\n\n\t\ttext {\n\t\t\tfont-size: 24rpx !important;\n\t\t\tline-height: 36rpx !important;\n\t\t}\n\t}\n\n\t::v-deep .u-number-box__minus--disabled {\n\t\tborder: 1px solid #ADADAD;\n\t}\n\n\t::v-deep .u-number-box__input {\n\t\tbackground-color: #fff !important;\n\t}\n\n\t.choose_yh {\n\t\tpadding-top: 40rpx;\n\t\twidth: 750rpx;\n\t\theight: 1106rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 20rpx 20rpx 0rpx 0rpx;\n\t\topacity: 1;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tz-index: 10088;\n\t\ttransition: all 0.5s;\n\n\t\t.head {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #171717;\n\t\t\ttext-align: center;\n\t\t\tmargin-bottom: 44rpx;\n\t\t}\n\n\t\t.close {\n\t\t\tposition: absolute;\n\t\t\ttop: 44rpx;\n\t\t\tright: 32rpx;\n\n\t\t\timage {\n\t\t\t\twidth: 37rpx;\n\t\t\t\theight: 37rpx;\n\t\t\t}\n\t\t}\n\n\t\t.cou_item {\n\t\t\tmargin: 0 auto;\n\t\t\twidth: 690rpx;\n\t\t\theight: 202rpx;\n\t\t\tbackground: #DCEAFF;\n\t\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tborder: 2rpx solid #2E80FE;\n\n\t\t\t.top {\n\t\t\t\theight: 150rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding-top: 26rpx;\n\t\t\t\tpadding-left: 24rpx;\n\t\t\t\tpadding-right: 14rpx;\n\t\t\t\tposition: relative;\n\t\t\t\tborder-bottom: 2rpx dashed #2E80FE;\n\n\t\t\t\t.box1 {\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\twidth: 180rpx;\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #E72427;\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.box2 {\n\t\t\t\t\tmargin-left: 28rpx;\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #171717;\n\t\t\t\t\t\tmax-width: 450rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #B2B2B2;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.box3 {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: 22rpx;\n\t\t\t\t\ttop: 40rpx;\n\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t\tbackground: #fff;\n\t\t\t\t\tborder: 2rpx solid #B2B2B2;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.bottom {\n\t\t\t\tpadding: 0 24rpx;\n\t\t\t\theight: 50rpx;\n\t\t\t\tmax-width: 500rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\tline-height: 50rpx;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #B2B2B2;\n\t\t\t}\n\t\t}\n\n\t\t.noYh {\n\t\t\twidth: 690rpx;\n\t\t\tmargin: 0 auto;\n\t\t\tmargin-top: 52rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 0 22rpx;\n\n\t\t\t.left {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #171717;\n\t\t\t}\n\n\t\t\t.right {\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t\tbackground: #fff;\n\t\t\t\tborder: 2rpx solid #B2B2B2;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t}\n\t\t}\n\n\t\t.notcan {\n\t\t\tmargin-top: 52rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #B2B2B2;\n\t\t\tpadding: 0 30rpx;\n\t\t}\n\t}\n\n\t.footer {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\theight: 202rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 30rpx;\n\t\tbackground-color: #fff;\n\n\t\t.left {\n\t\t\tfont-size: 40rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #E72427\n\t\t}\n\n\t\t.mid {\n\t\t\twidth: fit-content;\n\t\t\theight: 98rpx;\n\t\t\tborder-radius: 40rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\tline-height: 98rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-weight: 700;\n\t\t\tpadding: 0 15rpx;\n\t\t}\n\n\t\t.right {\n\t\t\twidth: 294rpx;\n\t\t\theight: 98rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\n\t\t\topacity: 1;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #FFFFFF;\n\t\t\tline-height: 98rpx;\n\t\t\ttext-align: center;\n\t\t}\n\t}\n\n\t.fg {\n\t\theight: 20rpx;\n\t\tbackground-color: #F3F4F5;\n\t}\n\n\t.address {\n\t\theight: 164rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 32rpx;\n\n\t\t.left {\n\t\t\t.top {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 36rpx;\n\t\t\t\t\theight: 36rpx;\n\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t}\n\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #171717;\n\t\t\t\t\tmax-width: 400rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.bottom {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #ADADAD;\n\t\t\t\tpadding-left: 56rpx;\n\t\t\t\tmargin-top: 12rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.main {\n\t\tpadding: 40rpx 32rpx;\n\t\tposition: relative;\n\t\tpadding-bottom: 70rpx;\n\n\t\t.expand {\n\t\t\twidth: 690rpx;\n\t\t\tmargin: 0 auto;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #ADADAD;\n\t\t\ttext-align: center;\n\t\t\tposition: absolute;\n\t\t\tbottom: 0;\n\n\t\t\t.icon_box {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: center;\n\t\t\t}\n\t\t}\n\n\t\t.main_item {\n\t\t\tdisplay: flex;\n\n\t\t\timage {\n\t\t\t\twidth: 160rpx;\n\t\t\t\theight: 160rpx;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\n\t\t\t.right {\n\t\t\t\tflex: 1;\n\n\t\t\t\t.title {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #171717;\n\t\t\t\t\tmax-width: 450rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\n\t\t\t\t.price {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\tmargin-top: 80rpx;\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.notes {\n\t\tpadding: 40rpx 32rpx;\n\n\t\t.title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #333333;\n\t\t}\n\n\t\ttextarea {\n\t\t\tmargin-top: 40rpx;\n\t\t\tpadding: 40rpx 30rpx;\n\t\t\tbox-sizing: border-box;\n\t\t\twidth: 686rpx;\n\t\t\theight: 242rpx;\n\t\t\tbackground: #F7F7F7;\n\t\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.preferential {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tpadding: 40rpx 32rpx;\n\t\talign-items: center;\n\n\t\t.left {\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #333333;\n\t\t}\n\n\t\t.right {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\ttext {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #E72427;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_order.vue?vue&type=style&index=0&id=f26050c2&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodong_order.vue?vue&type=style&index=0&id=f26050c2&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755740896000\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}