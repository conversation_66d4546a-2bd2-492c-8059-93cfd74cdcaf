(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/mine"],{

/***/ 209:
/*!********************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/main.js?{"page":"pages%2Fmine"} ***!
  \********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/uni-stat/dist/uni-stat.es.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _mine = _interopRequireDefault(__webpack_require__(/*! ./pages/mine.vue */ 210));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_mine.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 210:
/*!***************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mine.vue?vue&type=template&id=ef6e6e68& */ 211);
/* harmony import */ var _mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mine.vue?vue&type=script&lang=js& */ 213);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mine.vue?vue&type=style&index=0&lang=scss& */ 215);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 67);

var renderjs





/* normalize component */

var component = Object(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["render"],
  _mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/mine.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 211:
/*!**********************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?vue&type=template&id=ef6e6e68& ***!
  \**********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=template&id=ef6e6e68& */ 212);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 212:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?vue&type=template&id=ef6e6e68& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uIcon: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-icon/u-icon.vue */ 810))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 213:
/*!****************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=script&lang=js& */ 214);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 214:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 36));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 38));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _vuex = __webpack_require__(/*! vuex */ 48);
var _locationManager = _interopRequireDefault(__webpack_require__(/*! @/utils/location-manager.js */ 56));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var tabbar = function tabbar() {
  Promise.all(/*! require.ensure | components/tabbar */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/tabbar")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/tabbar.vue */ 835));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// Utility function for debouncing
var debounce = function debounce(func, wait) {
  var timeout;
  return function () {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    var context = this;
    clearTimeout(timeout);
    timeout = setTimeout(function () {
      return func.apply(context, args);
    }, wait);
  };
};
var _default = {
  components: {
    tabbar: tabbar
  },
  data: function data() {
    return {
      isLoading: false,
      inviteCode: '',
      tmplIds: ['', '', 'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'],
      code: '',
      // Store wx.login code
      loginPopupVisible: false,
      // Control login popup visibility
      agreedToTerms: false,
      // Control agreement checkbox state
      // 绑定手机号相关
      bindPhonePopupVisible: false,
      isBindingPhone: false,
      bindPhoneSmsCountdown: 0,
      bindPhoneSmsTimer: null,
      bindPhoneForm: {
        phone: '',
        code: ''
      },
      toolList: [{
        icon: 'coupon',
        text: '优惠券',
        url: '../user/coupon'
      }, {
        icon: 'map',
        text: '我的地址',
        url: '../user/address'
      }, {
        icon: 'level',
        text: '师傅入驻',
        url: '../shifu/Settle'
      },
      // {
      // 	icon: 'man-add',
      // 	text: '切换师傅版',
      // 	url: '../shifu/Receiving'
      // },
      {
        icon: 'red-packet-fill',
        text: '邀请有礼',
        url: '../user/promotion'
      }, {
        icon: 'man-add',
        text: '代理商申请',
        url: '../user/agent_apply'
      }, {
        icon: 'rmb-circle-fill',
        text: '提现管理',
        url: '../user/coreWallet'
      }],
      activityItem: {
        icon: 'heart-fill',
        text: '限时活动',
        url: '../user/huodong_index'
      },
      orderList: [{
        icon: 'order',
        text: '全部订单',
        url: '../user/order_list?tab=0',
        count: 0
      }, {
        icon: 'file-text',
        text: '报价列表',
        url: '../user/order_list?tab=-2',
        count: 0
      }, {
        icon: 'hourglass-half-fill',
        text: '待支付',
        url: '../user/order_list?tab=1',
        count: 0
      }, {
        icon: 'bell',
        text: '待服务',
        url: '../user/order_list?tab=5',
        count: 0
      }, {
        icon: 'clock',
        text: '服务中',
        url: '../user/order_list?tab=6',
        count: 0
      }, {
        icon: 'thumb-up',
        text: '已完成',
        url: '../user/order_list?tab=7',
        count: 0
      }]
    };
  },
  computed: _objectSpread(_objectSpread({}, (0, _vuex.mapState)({
    storeUserInfo: function storeUserInfo(state) {
      return state.user.userInfo || {};
    },
    token: function token(state) {
      return state.user.autograph || '';
    },
    erweima: function erweima(state) {
      return state.user.erweima || '';
    }
  })), {}, {
    isLoggedIn: function isLoggedIn() {
      // 如果有token，就认为已登录，即使用户信息还在加载中
      return !!this.token;
    },
    userInfo: function userInfo() {
      // 优先从Vuex获取，如果没有则从本地存储获取
      var vuexUserInfo = this.storeUserInfo;
      var localUserId = uni.getStorageSync('userId');
      var localPhone = uni.getStorageSync('phone');
      var localNickName = uni.getStorageSync('nickName');
      var localAvatarUrl = uni.getStorageSync('avatarUrl');
      var localPid = uni.getStorageSync('pid');
      var result = {
        phone: vuexUserInfo.phone || localPhone || '',
        avatarUrl: vuexUserInfo.avatarUrl || localAvatarUrl || '/static/mine/default_user.png',
        nickName: vuexUserInfo.nickName || localNickName || '微信用户',
        userId: vuexUserInfo.userId || localUserId || '',
        pid: vuexUserInfo.pid || localPid || ''
      };

      // 添加调试信息
      console.log('userInfo computed 被调用');
      console.log('vuexUserInfo:', vuexUserInfo);
      console.log('本地存储数据:', {
        userId: localUserId,
        phone: localPhone,
        nickName: localNickName,
        avatarUrl: localAvatarUrl,
        pid: localPid
      });
      console.log('最终userInfo结果:', result);
      return result;
    },
    canBindPhone: function canBindPhone() {
      return this.bindPhoneForm.phone && this.bindPhoneForm.code && this.validatePhone(this.bindPhoneForm.phone);
    }
  }),
  onLoad: function onLoad(options) {
    var _this = this;
    // Get current location
    this.getNowPosition();
    // Handle invite code from options
    if (options.inviteCode) {
      console.log('Received inviteCode:', options.inviteCode);
      this.inviteCode = options.inviteCode;
      uni.setStorageSync('receivedInviteCode', options.inviteCode);
    }
    // Handle erweima from Vuex or storage
    if (this.erweima) {
      console.log('erweima from Vuex:', this.erweima);
      this.inviteCode = this.erweima;
      uni.setStorageSync('receivedInviteCode', this.erweima);
    } else {
      var erweima = uni.getStorageSync('erweima');
      if (erweima) {
        console.log('erweima from storage:', erweima);
        this.$store.commit('setErweima', erweima);
        this.inviteCode = erweima;
        uni.setStorageSync('receivedInviteCode', erweima);
      }
    }
    // Perform WeChat login

    uni.login({
      provider: 'weixin',
      success: function success(res) {
        if (res.code) {
          _this.code = res.code;
          console.log('Initial wx.login code:', _this.code);
        }
      },
      fail: function fail(err) {
        console.error('wx.login failed:', err);
      }
    });

    // Fetch activity config
    // this.gethuodongconfig();
    // Initialize user data
    this.initUserData();
    // Fetch highlight if logged in
    if (this.isLoggedIn) {
      this.debounceGetHighlight();
    }
  },
  onShow: function onShow() {
    console.log('mine页面onShow，检查登录状态');
    console.log('token:', this.token);
    console.log('storeUserInfo:', this.storeUserInfo);

    // 如果有token，初始化用户数据
    if (this.token) {
      console.log('有token，初始化用户数据');
      this.initUserData();

      // 如果Vuex中没有用户信息，尝试获取
      if (!this.storeUserInfo.userId) {
        console.log('Vuex中没有用户信息，尝试获取');
        this.fetchUserInfo();
      }
      this.debounceGetHighlight();
    } else {
      console.log('没有token，处理未登录状态');
      // Ensure UI reflects logged-out state
      this.handleInvalidSession();
    }
  },
  onPullDownRefresh: function onPullDownRefresh() {
    // Handle pull-down refresh
    if (this.isLoggedIn && this.token) {
      Promise.all([this.fetchUserInfo(), this.getHighlight()]).then(function () {
        uni.stopPullDownRefresh();
        // this.showToast('刷新成功', 'success');
      }).catch(function (err) {
        console.error('Pull-down refresh failed:', err);
        uni.stopPullDownRefresh();
        // this.showToast('刷新失败，请稍后重试');
      });
    } else {
      // If not logged in, reset UI and stop refresh
      this.handleInvalidSession();
      uni.stopPullDownRefresh();
      this.showToast('请先登录');
    }
  },
  methods: _objectSpread(_objectSpread({
    getmylogin: function getmylogin() {
      var _this2 = this;
      uni.login({
        provider: 'weixin',
        success: function success(res) {
          if (res.code) {
            _this2.code = res.code;
            console.log('Initial wx.login code:', _this2.code);
          }
        }
      });
    },
    gethuodongconfig: function gethuodongconfig() {
      var _this3 = this;
      this.$api.service.huodongselectActivityConfig().then(function (res) {
        if (res.code === "200") {
          // Add activity item if not already present
          if (!_this3.toolList.some(function (item) {
            return item.text === _this3.activityItem.text;
          })) {
            _this3.toolList = [].concat((0, _toConsumableArray2.default)(_this3.toolList), [_this3.activityItem]);
          }
        } else {
          // Remove activity item if present
          _this3.toolList = _this3.toolList.filter(function (item) {
            return item.text !== _this3.activityItem.text;
          });
        }
        console.log('huodongselectActivityConfig response:', res);
      }).catch(function (err) {
        console.error('huodongselectActivityConfig failed:', err);
        // Ensure activity item is removed on error
        _this3.toolList = _this3.toolList.filter(function (item) {
          return item.text !== _this3.activityItem.text;
        });
      });
    },
    getNowPosition: function getNowPosition() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var locationData;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                _context.next = 3;
                return _locationManager.default.getLocation({
                  forceUpdate: false,
                  silent: true
                });
              case 3:
                locationData = _context.sent;
                if (locationData && locationData.city) {
                  _this4.position = locationData.city;
                }
                console.log("定位获取成功:", locationData);
                _context.next = 11;
                break;
              case 8:
                _context.prev = 8;
                _context.t0 = _context["catch"](0);
                console.error("获取定位失败:", _context.t0);
                // 定位失败不影响页面功能
              case 11:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 8]]);
      }))();
    },
    getshifuinfo: function getshifuinfo() {
      this.$api.shifu.checkMaster().then(function (res) {
        if (res.code === "200") {
          uni.redirectTo({
            url: '../shifu/mine'
          });
        }
      });
    },
    // Debounced getHighlight to prevent multiple rapid calls
    debounceGetHighlight: debounce(function () {
      this.getHighlight();
    }, 300),
    getHighlight: function getHighlight() {
      var _this5 = this;
      var userId = uni.getStorageSync('userId');
      if (!userId) {
        console.log('No userId, skipping getHighlight');
        return Promise.resolve();
      }
      this.isLoading = true;
      return this.$api.service.getHighlight({
        userId: userId,
        role: 1
      }).then(function (res) {
        console.log('getHighlight response:', res);
        // Create a new array to ensure reactivity
        var updatedOrderList = _this5.orderList.map(function (item, index) {
          return _objectSpread(_objectSpread({}, item), {}, {
            count: index === 0 ? res && res.countOrder ? res.countOrder : 0 : index === 1 ? res && res.shiFuBaoJia ? res.shiFuBaoJia : 0 : index === 2 ? res && res.daiZhiFu ? res.daiZhiFu : 0 : index === 3 ? res && res.daiFuWu ? res.daiFuWu : 0 : index === 4 ? res && res.fuWuZhong ? res.fuWuZhong : 0 : index === 5 ? res && res.yiWanCheng ? res.yiWanCheng : 0 : 0
          });
        });
        // Update orderList reactively
        _this5.$set(_this5, 'orderList', updatedOrderList);
      }).finally(function () {
        _this5.isLoading = false;
      });
    },
    handleContact: function handleContact(e) {
      console.log(e.detail.path);
      console.log(e.detail.query);
    },
    // APP端客服电话拨打功能
    callCustomerService: function callCustomerService() {
      var phoneNumber = '13966580997';
      uni.makePhoneCall({
        phoneNumber: phoneNumber,
        success: function success() {
          console.log('拨打客服电话成功');
        },
        fail: function fail(err) {
          console.error('拨打客服电话失败:', err);
          uni.showToast({
            title: '拨打失败，请稍后重试',
            icon: 'none'
          });
        }
      });
    }
  }, (0, _vuex.mapMutations)(['updateUserItem'])), {}, {
    // 获取当前平台类型
    getCurrentPlatform: function getCurrentPlatform() {
      return 'mp-weixin';
      return 'unknown';
    },
    showLoginPopup: function showLoginPopup() {
      var platform = this.getCurrentPlatform();
      console.log('当前平台:', platform);

      // 检查当前运行环境

      console.log('非APP端显示登录弹窗，isapp: 0');
      this.loginPopupVisible = true;

      // 运行时检查作为备用方案
      var systemInfo = uni.getSystemInfoSync();
      console.log('系统信息:', {
        platform: systemInfo.platform,
        app: systemInfo.app,
        uniPlatform: platform
      });
    },
    dingyue: function dingyue() {
      var _this6 = this;
      var panduan = uni.getStorageSync('userId');
      console.log(panduan);
      if (panduan) {
        console.log('dingyue called');
        var allTmplIds = this.tmplIds;
        if (allTmplIds.length < 3) {
          console.error("Not enough template IDs available:", allTmplIds);
          return;
        }
        var shuffled = (0, _toConsumableArray2.default)(allTmplIds).sort(function () {
          return 0.5 - Math.random();
        });
        var selectedTmplIds = shuffled.slice(0, 3);
        console.log("Selected template IDs:", selectedTmplIds);
        var templateData = selectedTmplIds.map(function (id, index) {
          return {
            templateId: id,
            templateCategoryId: index === 0 ? 10 : 5
          };
        });
        uni.requestSubscribeMessage({
          tmplIds: selectedTmplIds,
          success: function success(res) {
            console.log('requestSubscribeMessage success:', res, 'with tmplIds:', _this6.tmplIds);
            // Check if any of the template IDs were rejected
            var hasRejection = _this6.tmplIds.some(function (tmplId) {
              return res[tmplId] === 'reject';
            });
            var hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
            if (hasRejection && !hasShownModal) {
              uni.showModal({
                title: '提示',
                content: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。',
                cancelText: '取消',
                confirmText: '去开启',
                confirmColor: '#007AFF',
                success: function success(modalRes) {
                  uni.setStorageSync('hasShownSubscriptionModal', true);
                  if (modalRes.confirm) {
                    uni.openSetting({
                      withSubscriptions: true
                    });
                  } else if (modalRes.cancel) {
                    uni.setStorageSync('hasCanceledSubscription', true);
                  }
                }
              });
            }
            _this6.templateCategoryIds = [];
            selectedTmplIds.forEach(function (templId, index) {
              console.log("Template ".concat(templId, " status: ").concat(res[templId]));
              if (res[templId] === 'accept') {
                var templateCategoryId = templateData[index].templateCategoryId;
                if (templateCategoryId === 10) {
                  for (var i = 0; i < 15; i++) {
                    _this6.templateCategoryIds.push(templateCategoryId);
                  }
                } else {
                  _this6.templateCategoryIds.push(templateCategoryId);
                }
                console.log('Accepted message push for template:', templId);
              }
            });
            console.log('Updated templateCategoryIds:', _this6.templateCategoryIds);
          },
          fail: function fail(err) {
            console.error('requestSubscribeMessage failed:', err);
          }
        });
      }
    },
    hideLoginPopup: function hideLoginPopup() {
      this.loginPopupVisible = false;
      this.agreedToTerms = false;
    },
    toggleAgreement: function toggleAgreement() {
      this.agreedToTerms = !this.agreedToTerms;
    },
    // 绑定手机号相关方法
    showBindPhonePopup: function showBindPhonePopup() {
      this.bindPhonePopupVisible = true;
    },
    hideBindPhonePopup: function hideBindPhonePopup() {
      this.bindPhonePopupVisible = false;
      this.bindPhoneForm = {
        phone: '',
        code: ''
      };
      if (this.bindPhoneSmsTimer) {
        clearInterval(this.bindPhoneSmsTimer);
        this.bindPhoneSmsTimer = null;
        this.bindPhoneSmsCountdown = 0;
      }
    },
    // 验证手机号
    validatePhone: function validatePhone(phone) {
      var phoneReg = /^1[3-9]\d{9}$/;
      return phoneReg.test(phone);
    },
    // 发送绑定手机号验证码
    sendBindPhoneSmsCode: function sendBindPhoneSmsCode() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var phone, response;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (!(_this7.bindPhoneSmsCountdown > 0)) {
                  _context2.next = 2;
                  break;
                }
                return _context2.abrupt("return");
              case 2:
                phone = _this7.bindPhoneForm.phone;
                if (_this7.validatePhone(phone)) {
                  _context2.next = 5;
                  break;
                }
                return _context2.abrupt("return", _this7.showToast('请输入正确的手机号'));
              case 5:
                _context2.prev = 5;
                _context2.next = 8;
                return _this7.$api.base.sendSmsCode({
                  phone: phone
                });
              case 8:
                response = _context2.sent;
                if (response.code === '200') {
                  _this7.showToast('验证码发送成功', 'success');
                  _this7.startBindPhoneCountdown();
                } else {
                  _this7.showToast(response.msg || '验证码发送失败，请重试');
                }
                _context2.next = 16;
                break;
              case 12:
                _context2.prev = 12;
                _context2.t0 = _context2["catch"](5);
                console.error('发送验证码失败:', _context2.t0);
                _this7.showToast('验证码发送失败，请重试');
              case 16:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[5, 12]]);
      }))();
    },
    // 开始绑定手机号倒计时
    startBindPhoneCountdown: function startBindPhoneCountdown() {
      var _this8 = this;
      this.bindPhoneSmsCountdown = 60;
      this.bindPhoneSmsTimer = setInterval(function () {
        _this8.bindPhoneSmsCountdown--;
        if (_this8.bindPhoneSmsCountdown <= 0) {
          clearInterval(_this8.bindPhoneSmsTimer);
          _this8.bindPhoneSmsTimer = null;
        }
      }, 1000);
    },
    navigateToAgreement: function navigateToAgreement(type) {
      var url = '../user/configuser';
      if (type === 'service') {
        url += '?type=service';
      } else if (type === 'privacy') {
        url += '?type=privacy';
      }
      uni.navigateTo({
        url: url
      });
    },
    // 处理绑定手机号
    handleBindPhone: function handleBindPhone() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var _this9$bindPhoneForm, phone, code, unionid, registerID, params, response, updatedUserInfo;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (!(!_this9.canBindPhone || _this9.isBindingPhone)) {
                  _context3.next = 2;
                  break;
                }
                return _context3.abrupt("return");
              case 2:
                _this9$bindPhoneForm = _this9.bindPhoneForm, phone = _this9$bindPhoneForm.phone, code = _this9$bindPhoneForm.code;
                if (_this9.validatePhone(phone)) {
                  _context3.next = 5;
                  break;
                }
                return _context3.abrupt("return", _this9.showToast('请输入正确的手机号'));
              case 5:
                if (code) {
                  _context3.next = 7;
                  break;
                }
                return _context3.abrupt("return", _this9.showToast('请输入验证码'));
              case 7:
                _this9.isBindingPhone = true;
                uni.showLoading({
                  title: '绑定中...'
                });
                _context3.prev = 9;
                // 获取unionid
                unionid = uni.getStorageSync('unionid');
                if (unionid) {
                  _context3.next = 13;
                  break;
                }
                throw new Error('缺少微信用户标识，请重新登录');
              case 13:
                registerID = uni.getStorageSync("registerID"); // 调用绑定接口
                params = {
                  phone: phone,
                  shortCode: code,
                  unionid: unionid,
                  platform: 2,
                  // 用户端
                  registrationId: registerID // 极光推送id，暂时为空
                };

                console.log('绑定手机号参数:', params);
                _context3.next = 18;
                return _this9.$api.user.register(params);
              case 18:
                response = _context3.sent;
                console.log('绑定手机号响应:', response);
                if (!(response.code === '200')) {
                  _context3.next = 29;
                  break;
                }
                _this9.showToast('绑定成功', 'success');

                // 更新用户信息
                updatedUserInfo = _objectSpread(_objectSpread({}, _this9.storeUserInfo), {}, {
                  phone: phone
                });
                _this9.updateUserItem({
                  key: 'userInfo',
                  val: updatedUserInfo
                });

                // 更新本地存储
                uni.setStorageSync('phone', phone);

                // 关闭弹窗
                _this9.hideBindPhonePopup();

                // 刷新用户信息
                _this9.fetchUserInfo();
                _context3.next = 30;
                break;
              case 29:
                throw new Error(response.msg || '绑定失败，请重试');
              case 30:
                _context3.next = 36;
                break;
              case 32:
                _context3.prev = 32;
                _context3.t0 = _context3["catch"](9);
                console.error('绑定手机号失败:', _context3.t0);
                _this9.showToast(_context3.t0.message || '绑定失败，请重试');
              case 36:
                _context3.prev = 36;
                _this9.isBindingPhone = false;
                uni.hideLoading();
                return _context3.finish(36);
              case 40:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[9, 32, 36, 40]]);
      }))();
    },
    initUserData: function initUserData() {
      console.log('initUserData被调用');
      console.log('当前token:', this.token);
      console.log('当前storeUserInfo:', this.storeUserInfo);
      if (this.token && !this.storeUserInfo.userId) {
        console.log('有token但Vuex中没有用户信息，从本地存储恢复');
        var userInfo = {
          phone: uni.getStorageSync('phone') || '',
          avatarUrl: uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
          nickName: uni.getStorageSync('nickName') || '微信用户',
          userId: uni.getStorageSync('userId') || '',
          pid: uni.getStorageSync('pid') || '',
          unionid: uni.getStorageSync('unionid') || ''
        };
        console.log('从本地存储获取的用户信息:', userInfo);

        // 只要有userId就保存用户信息（微信登录可能没有手机号）
        if (userInfo.userId) {
          console.log('保存用户信息到Vuex');
          this.updateUserItem({
            key: 'userInfo',
            val: userInfo
          });
        } else {
          console.log('没有userId，处理无效会话');
          this.handleInvalidSession();
        }
      } else if (this.token && this.storeUserInfo.userId) {
        console.log('token和用户信息都存在，无需初始化');
      } else {
        console.log('没有token，跳过初始化');
      }
    },
    navigateTo: function navigateTo(url) {
      if (!url) return;

      // 特殊处理师傅入驻功能
      if (url === '../shifu/Settle') {
        this.handleMasterSettlement();
        return;
      }
      var requiresLogin = ['../user/coupon',
      // toolList 中的优惠券
      '../user/repair_record', '../user/order_list',
      // orderList 中的所有订单页面
      '../user/address',
      // toolList 中的我的地址
      '../user/userProfile',
      // 用户资料页面
      '../user/Settle', '../user/agent_apply',
      // toolList 中的代理商申请
      '../user/promotion',
      // toolList 中的邀请有礼
      '../user/coreWallet',
      // toolList 中的提现管理
      '../user/bankCard', '../shifu/Settle', '../shifu/Receiving', '../shifu/mine'];
      if (requiresLogin.some(function (path) {
        return url.startsWith(path);
      }) && !this.isLoggedIn) {
        return this.showLoginPopup();
      }
      uni.navigateTo({
        url: url
      });
    },
    fetchUserInfo: function fetchUserInfo() {
      var _this10 = this;
      if (this.isLoading || !this.token) {
        console.log('Skipping fetchUserInfo: no token or already loading');
        return Promise.resolve();
      }
      this.isLoading = true;
      return this.$api.user.userInfo().then(function (responses) {
        var response = responses.data;
        if (!response || (0, _typeof2.default)(response) !== 'object') {
          throw new Error('获取用户信息失败: 响应数据无效');
        }
        var userInfo = {
          phone: response.phone || '',
          avatarUrl: response.avatarUrl || '/static/mine/default_user.png',
          nickName: response.nickName || '微信用户',
          userId: response.id || '',
          createTime: response.createTime || '',
          pid: response.pid || '',
          inviteCode: response.inviteCode || ''
        };
        _this10.updateUserItem({
          key: 'userInfo',
          val: userInfo
        });
        _this10.saveUserInfoToStorage(userInfo);
      }).catch(function (error) {
        console.error('获取用户信息失败:', error);
        if (error.message && error.message.includes('响应数据无效')) {
          _this10.handleInvalidSession();
        } else {
          if (_this10.token) {
            _this10.showToast('获取用户信息失败，请稍后重试');
          }
        }
      }).finally(function () {
        _this10.isLoading = false;
      });
    },
    saveUserInfoToStorage: function saveUserInfoToStorage(userInfo) {
      uni.setStorageSync('phone', userInfo.phone);
      uni.setStorageSync('avatarUrl', userInfo.avatarUrl);
      uni.setStorageSync('nickName', userInfo.nickName);
      uni.setStorageSync('userId', userInfo.userId);
      uni.setStorageSync('pid', userInfo.pid);
      if (userInfo.unionid) {
        uni.setStorageSync('unionid', userInfo.unionid);
      }
    },
    handleInvalidSession: function handleInvalidSession() {
      ['token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid', 'unionid'].forEach(function (key) {
        uni.removeStorageSync(key);
      });
      this.updateUserItem({
        key: 'userInfo',
        val: {}
      });
      this.updateUserItem({
        key: 'autograph',
        val: ''
      });
      this.isLoading = false;
      // Reset orderList counts to 0 when session is invalid
      this.$set(this, 'orderList', this.orderList.map(function (item) {
        return _objectSpread(_objectSpread({}, item), {}, {
          count: 0
        });
      }));
    },
    onGetPhoneNumber: function onGetPhoneNumber(e) {
      var _this11 = this;
      console.log('微信授权登录');
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        return this.showToast('授权失败，请重试');
      }
      this.getmylogin();
      this.isLoading = true;
      uni.showLoading({
        mask: true,
        title: '登录中...'
      });
      var _e$detail = e.detail,
        encryptedData = _e$detail.encryptedData,
        iv = _e$detail.iv;
      uni.checkSession({
        success: function success() {
          _this11.loginWithWeixin({
            code: _this11.code,
            encryptedData: encryptedData,
            iv: iv,
            platform: 2,
            pid: _this11.inviteCode
          });
        },
        fail: function fail() {
          uni.login({
            provider: 'weixin',
            success: function success(res) {
              if (res.code) {
                _this11.code = res.code;
                console.log('Refreshed wx.login code:', _this11.code);
                _this11.loginWithWeixin({
                  code: _this11.code,
                  encryptedData: encryptedData,
                  iv: iv,
                  platform: 2,
                  pid: _this11.inviteCode
                });
              } else {
                _this11.isLoading = false;
                uni.hideLoading();
                _this11.showToast('获取登录凭证失败');
              }
            },
            fail: function fail() {
              _this11.isLoading = false;
              uni.hideLoading();
              _this11.showToast('微信登录失败，请重试');
            }
          });
        }
      });
    },
    loginWithWeixin: function loginWithWeixin(params) {
      var _this12 = this;
      // 获取平台类型
      var isapp = this.getCurrentPlatform() === 'app-plus' ? 1 : 0;
      console.log('微信小程序登录平台类型 isapp:', isapp);
      this.$api.user.loginuserInfo({
        code: params.code,
        encryptedData: params.encryptedData,
        iv: params.iv,
        platform: 2,
        pid: this.inviteCode,
        isapp: isapp // 添加平台类型参数
      }).then(function (response) {
        console.log(response);
        if (!response || !response.data.token) {
          throw new Error('请重新登录');
        }
        uni.setStorageSync('token', response.data.token);
        _this12.updateUserItem({
          key: 'autograph',
          val: response.data.token
        });
        return _this12.$api.user.userInfo();
      }).then(function (userInfoRess) {
        var userInfoRes = userInfoRess.data;
        if (!userInfoRes || (0, _typeof2.default)(userInfoRes) !== 'object') {
          throw new Error('获取用户信息失败');
        }
        var userInfo = {
          phone: userInfoRes.phone || '',
          avatarUrl: userInfoRes.avatarUrl || '/static/mine/default_user.png',
          nickName: userInfoRes.nickName || '微信用户',
          userId: userInfoRes.id || '',
          createTime: userInfoRes.createTime || '',
          pid: userInfoRes.pid || ''
        };
        _this12.updateUserItem({
          key: 'userInfo',
          val: userInfo
        });
        _this12.saveUserInfoToStorage(userInfo);
        _this12.showToast('登录成功', 'success');
        _this12.hideLoginPopup();
        _this12.debounceGetHighlight();
        // this.getshifuinfo();
      }).catch(function (error) {
        console.error('Login error:', error);
        _this12.showToast(error.message || '登录失败，请稍后重试');
        _this12.handleInvalidSession();
      }).finally(function () {
        _this12.isLoading = false;
        uni.hideLoading();
      });
    },
    showToast: function showToast(title) {
      var icon = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';
      uni.showToast({
        title: title,
        icon: icon,
        duration: 2000
      });
    },
    // 处理师傅入驻功能
    handleMasterSettlement: function handleMasterSettlement() {
      var _this13 = this;
      console.log('师傅入驻功能被点击');
      var platform = this.getCurrentPlatform();
      console.log('当前平台:', platform);

      // 微信小程序跳转到另一个小程序
      console.log('微信小程序跳转到师傅端小程序');
      uni.navigateToMiniProgram({
        appId: 'wx9bdd97c5d5c5fcec',
        path: 'shifu/Settle',
        success: function success(res) {
          console.log('跳转师傅端小程序成功:', res);
        },
        fail: function fail(err) {
          console.error('跳转师傅端小程序失败:', err);
          _this13.showToast('跳转失败，请稍后重试');
        }
      });
    }
  })
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 215:
/*!*************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?vue&type=style&index=0&lang=scss& ***!
  \*************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=style&index=0&lang=scss& */ 216);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 216:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?vue&type=style&index=0&lang=scss& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[209,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/mine.js.map