(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["shifu/Receiving"],{

/***/ 627:
/*!*************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/main.js?{"page":"shifu%2FReceiving"} ***!
  \*************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/uni-stat/dist/uni-stat.es.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _Receiving = _interopRequireDefault(__webpack_require__(/*! ./shifu/Receiving.vue */ 628));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_Receiving.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 628:
/*!********************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue ***!
  \********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Receiving_vue_vue_type_template_id_23563916_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Receiving.vue?vue&type=template&id=23563916&scoped=true& */ 629);
/* harmony import */ var _Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Receiving.vue?vue&type=script&lang=js& */ 631);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _Receiving_vue_vue_type_style_index_0_id_23563916_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Receiving.vue?vue&type=style&index=0&id=23563916&scoped=true&lang=scss& */ 633);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 67);

var renderjs





/* normalize component */

var component = Object(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _Receiving_vue_vue_type_template_id_23563916_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _Receiving_vue_vue_type_template_id_23563916_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "23563916",
  null,
  false,
  _Receiving_vue_vue_type_template_id_23563916_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "shifu/Receiving.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 629:
/*!***************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?vue&type=template&id=23563916&scoped=true& ***!
  \***************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_template_id_23563916_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Receiving.vue?vue&type=template&id=23563916&scoped=true& */ 630);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_template_id_23563916_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_template_id_23563916_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_template_id_23563916_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_template_id_23563916_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 630:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?vue&type=template&id=23563916&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uSwiper: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-swiper/u-swiper */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-swiper/u-swiper")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-swiper/u-swiper.vue */ 819))
    },
    uTag: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-tag/u-tag */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-tag/u-tag")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-tag/u-tag.vue */ 1027))
    },
    uEmpty: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-empty/u-empty */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-empty/u-empty")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-empty/u-empty.vue */ 891))
    },
    uIcon: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-icon/u-icon.vue */ 810))
    },
    uPopup: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-popup/u-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-popup/u-popup.vue */ 1001))
    },
    "u-Input": function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u--input/u--input */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u--input/u--input")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u--input/u--input.vue */ 995))
    },
    uModal: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-modal/u-modal */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-modal/u-modal")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-modal/u-modal.vue */ 850))
    },
    uLoadmore: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-loadmore/u-loadmore */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-loadmore/u-loadmore")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-loadmore/u-loadmore.vue */ 899))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.list.length
  var l0 = _vm.__map(_vm.list, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var g1 = item.mobile.slice(0, 3)
    return {
      $orig: $orig,
      g1: g1,
    }
  })
  var g2 = _vm.list.length
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.confirmshow = false
    }
    _vm.e1 = function ($event) {
      _vm.masterModalShow = false
    }
    _vm.e2 = function ($event) {
      _vm.detailModalShow = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        g2: g2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 631:
/*!*********************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Receiving.vue?vue&type=script&lang=js& */ 632);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 632:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 36));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 38));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _vuex = __webpack_require__(/*! vuex */ 48);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var tabbar = function tabbar() {
  __webpack_require__.e(/*! require.ensure | components/tabbarsf */ "components/tabbarsf").then((function () {
    return resolve(__webpack_require__(/*! @/components/tabbarsf.vue */ 1035));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    tabbar: tabbar
  },
  data: function data() {
    return {
      orderData: '',
      showDingyue: false,
      showCate: false,
      infodata: '',
      tmplIds: ['', '', 'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'],
      status: 'loadmore',
      id: '',
      shifuId: '',
      list: [],
      show: false,
      confirmshow: false,
      masterModalShow: false,
      detailModalShow: false,
      // Added for new modal
      content: '确认接下该订单吗',
      input: '',
      area_id: '',
      limit: 50,
      page: 1,
      bannerList: [],
      configInfo: '',
      getconfigs: '',
      list1: [],
      lng: '',
      shifustutus: {
        // Initialize shifustutus as an object to hold 'data' and 'msg'
        data: 0,
        msg: ''
      },
      lat: '',
      cateList: [],
      currentCateId: '',
      currentCateName: '选择接单分类',
      copyCateList: [],
      province: '',
      city: '',
      district: '',
      isPageLoaded: false,
      // 添加页面加载状态标识
      selectedItem: null // Added to store the selected item
    };
  },

  computed: _objectSpread({}, (0, _vuex.mapState)({
    configInfos: function configInfos(state) {
      return state.config.configInfo;
    },
    refreshReceiving: function refreshReceiving(state) {
      return state.service.refreshReceiving || '';
    } // Map Vuex state
  })),

  methods: {
    dingyue: function dingyue() {
      var _this = this;
      console.log('dingyue called');
      var allTmplIds = this.tmplIds;
      if (allTmplIds.length < 3) {
        console.error("Not enough template IDs available:", allTmplIds);
        // uni.showToast({
        // 	icon: 'none',
        // 	title: '模板ID不足'
        // });
        return;
      }
      var shuffled = (0, _toConsumableArray2.default)(allTmplIds).sort(function () {
        return 0.5 - Math.random();
      });
      var selectedTmplIds = shuffled.slice(0, 3);
      console.log("Selected template IDs:", selectedTmplIds);
      var templateData = selectedTmplIds.map(function (id, index) {
        return {
          templateId: id,
          templateCategoryId: index === 0 ? 10 : 5
        };
      });
      uni.requestSubscribeMessage({
        tmplIds: selectedTmplIds,
        success: function success(res) {
          console.log('requestSubscribeMessage success:', res, 'with tmplIds:', _this.tmplIds);
          // Check if any of the template IDs were rejected
          var hasRejection = _this.tmplIds.some(function (tmplId) {
            return res[tmplId] === 'reject';
          });
          var hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
          if (hasRejection && !hasShownModal) {
            uni.showModal({
              title: '提示',
              content: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收用户订单通知。',
              cancelText: '取消',
              confirmText: '去开启',
              confirmColor: '#007AFF',
              success: function success(modalRes) {
                uni.setStorageSync('hasShownSubscriptionModal', true);
                if (modalRes.confirm) {
                  uni.openSetting({
                    withSubscriptions: true
                  });
                } else if (modalRes.cancel) {
                  uni.setStorageSync('hasCanceledSubscription', true);
                }
              }
            });
          }
          _this.templateCategoryIds = [];
          selectedTmplIds.forEach(function (templId, index) {
            console.log("Template ".concat(templId, " status: ").concat(res[templId]));
            if (res[templId] === 'accept') {
              var templateCategoryId = templateData[index].templateCategoryId;
              if (templateCategoryId === 10) {
                for (var i = 0; i < 15; i++) {
                  _this.templateCategoryIds.push(templateCategoryId);
                }
              } else {
                _this.templateCategoryIds.push(templateCategoryId);
              }
              console.log('Accepted message push for template:', templId);
            }
          });
          console.log('Updated templateCategoryIds:', _this.templateCategoryIds);
        },
        fail: function fail(err) {
          console.error('requestSubscribeMessage failed:', err);
        }
      });
    },
    // 输入验证方法
    validateInput: function validateInput(e) {
      var value = e.detail ? e.detail.value : e;

      // 移除所有中文字符
      value = value.replace(/[\u4e00-\u9fa5]/g, '');

      // 只允许数字和小数点
      value = value.replace(/[^\d.]/g, '');

      // 处理小数点逻辑
      var parts = value.split('.');
      if (parts.length > 2) {
        // 如果有多个小数点，只保留第一个
        value = parts[0] + '.' + parts.slice(1).join('');
      }
      if (parts.length === 2) {
        // 如果有小数部分，限制小数点后只能有两位
        if (parts[1].length > 2) {
          parts[1] = parts[1].substring(0, 2);
          value = parts[0] + '.' + parts[1];
        }
      }

      // 防止以多个0开头（除了0.开头的情况）
      if (value.length > 1 && value.charAt(0) === '0' && value.charAt(1) !== '.') {
        value = value.substring(1);
      }

      // 更新input值
      this.input = value;
    },
    reset: function reset() {
      this.currentCateName = '选择接单分类';
      this.currentCateId = '';
      this.page = 1;
      this.getList();
    },
    textsss: function textsss() {
      if (this.infodata.status === 2) {
        uni.requestSubscribeMessage({
          tmplIds: this.tmplIds,
          success: function success(res) {
            console.log('requestSubscribeMessage result:', res);
          },
          fail: function fail(err) {
            console.log('requestSubscribeMessage failed:', err);
          }
        });
      }
    },
    closeCate: function closeCate() {
      var _this2 = this;
      this.showCate = false;
      setTimeout(function () {
        _this2.cateList = _this2.copyCateList;
      }, 500);
    },
    chooseCate: function chooseCate() {
      this.showCate = !this.showCate;
    },
    asyncChange: function asyncChange() {
      uni.showModal({
        title: '订阅提示',
        content: '请手动开启消息订阅以获取订单状态提醒',
        success: function success(res) {
          uni.navigateTo({
            url: '/shifu/userProfile'
          });
        },
        fail: function fail(err) {
          // console.log('uni.showModal failed:', err);
          // uni.showToast({
          // 	title: '显示订阅提示失败',
          // 	icon: 'none'
          // });
        }
      });
    },
    confirmSubscription: function confirmSubscription() {
      var _this3 = this;
      uni.openSetting({
        withSubscriptions: true,
        success: function success() {
          console.log('uni.openSetting success');
          uni.getSetting({
            withSubscriptions: true,
            success: function success(res) {
              var subscriptions = res.subscriptionsSetting;
              console.log('uni.getSetting result:', subscriptions);
              if (subscriptions.mainSwitch) {
                _this3.requestSubscription();
              } else {
                _this3.showDingyue = true;
                uni.showToast({
                  title: '订阅取消',
                  icon: 'none'
                });
                _this3.checkSubscriptionStatus();
              }
            },
            fail: function fail(err) {
              console.log('uni.getSetting failed:', err);
              _this3.showDingyue = true;
              uni.showToast({
                title: '获取设置失败',
                icon: 'none'
              });
            }
          });
        },
        fail: function fail(err) {
          console.log('uni.openSetting failed:', err);
          _this3.showDingyue = true;
          uni.showToast({
            title: '打开设置失败，请稍后重试',
            icon: 'none'
          });
        }
      });
    },
    cancelSubscription: function cancelSubscription() {
      this.checkSubscriptionStatus();
      uni.showToast({
        title: '订阅取消',
        icon: 'none'
      });
    },
    requestSubscription: function requestSubscription() {
      var _this4 = this;
      var infodata = JSON.parse(uni.getStorageSync('shiInfo'));
      console.log(infodata);
      if (infodata.status === 2) {
        uni.requestSubscribeMessage({
          tmplIds: this.tmplIds,
          success: function success(res) {
            console.log('requestSubscribeMessage result:', res);
            var anyAccepted = _this4.tmplIds.some(function (id) {
              return res[id] === 'accept';
            });
            if (anyAccepted) {
              _this4.showDingyue = false;
              uni.showToast({
                title: '消息订阅成功',
                icon: 'success'
              });
            } else {
              _this4.showDingyue = true;
              uni.showToast({
                title: '订阅取消',
                icon: 'none'
              });
            }
            _this4.checkSubscriptionStatus();
          },
          fail: function fail(err) {
            console.log('requestSubscribeMessage failed:', err);
            _this4.showDingyue = true;
            _this4.checkSubscriptionStatus();
          }
        });
      }
    },
    checkSubscriptionStatus: function checkSubscriptionStatus() {
      var _this5 = this;
      uni.getSetting({
        withSubscriptions: true,
        success: function success(res) {
          var subscriptions = res.subscriptionsSetting;
          if (subscriptions.mainSwitch) {
            var anySubscribed = _this5.tmplIds.some(function (id) {
              return subscriptions.itemSettings && subscriptions.itemSettings[id] === 'accept';
            });
            _this5.showDingyue = !anySubscribed;
          } else {
            _this5.showDingyue = true;
          }
          console.log('Updated showDingyue:', _this5.showDingyue);
        },
        fail: function fail(err) {
          console.log('checkSubscriptionStatus failed:', err);
          _this5.showDingyue = true;
          uni.showToast({
            title: '检查订阅状态失败',
            icon: 'none'
          });
        }
      });
    },
    selectClick: function selectClick(cate) {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var res, count;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!cate.id) {
                  _context.next = 18;
                  break;
                }
                _context.prev = 1;
                _context.next = 4;
                return _this6.$api.shifu.indexQuote({
                  lng: _this6.lng,
                  lat: _this6.lat,
                  parentId: cate.id
                });
              case 4:
                res = _context.sent;
                if (res.code === "-1") {
                  uni.showToast({
                    icon: 'none',
                    title: res.msg
                  }, 3000);
                }
                _this6.showCate = false;
                _this6.currentCateName = cate.name;
                _this6.currentCateId = cate.id;
                _this6.list = res.data.list || [];
                count = _this6.list.length;
                console.log('List count:', count);
                uni.setStorageSync('listCount', count);
                _this6.$forceUpdate();
                _context.next = 18;
                break;
              case 16:
                _context.prev = 16;
                _context.t0 = _context["catch"](1);
              case 18:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[1, 16]]);
      }))();
    },
    getCate: function getCate() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return _this7.$api.shifu.serviceCate();
              case 3:
                res = _context2.sent;
                _this7.cateList = res || [];
                _this7.copyCateList = res || [];
                _context2.next = 11;
                break;
              case 8:
                _context2.prev = 8;
                _context2.t0 = _context2["catch"](0);
                uni.showToast({
                  icon: 'none',
                  title: '获取分类失败'
                });
              case 11:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 8]]);
      }))();
    },
    seeDetail: function seeDetail(item) {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _this8.$api.shifu.getshifstutas().then(function (res) {
                  console.log("shifustutus response:", res);
                  _this8.shifustutus = res; // Assign the whole response object
                });
                _context3.prev = 1;
                _this8.selectedItem = item; // Store the selected item
                if (!(_this8.shifustutus.data === -2)) {
                  _context3.next = 7;
                  break;
                }
                // If shifustutus.data is -2, show the "成为师傅才能操作" modal
                _this8.masterModalShow = true;
                _context3.next = 13;
                break;
              case 7:
                if (!(_this8.shifustutus.data === -1)) {
                  _context3.next = 12;
                  break;
                }
                // If shifustutus.data is -1, show the message from shifustutus.msg
                uni.showToast({
                  icon: 'none',
                  title: _this8.shifustutus.msg || '无法进行此操作'
                });
                return _context3.abrupt("return");
              case 12:
                // For other shifustutus.data values, show the "服务承诺" modal
                _this8.detailModalShow = true;
              case 13:
                _context3.next = 18;
                break;
              case 15:
                _context3.prev = 15;
                _context3.t0 = _context3["catch"](1);
                uni.showToast({
                  icon: 'none',
                  title: '检查身份失败'
                });
              case 18:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[1, 15]]);
      }))();
    },
    confirmDetail: function confirmDetail() {
      if (this.selectedItem) {
        uni.setStorageSync('selectedOrder', this.selectedItem);
        uni.navigateTo({
          url: "/shifu/master_order_details?id=".concat(this.selectedItem.id)
        });
      }
      this.detailModalShow = false;
      this.selectedItem = null;
    },
    getList: function getList() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var location, geoRes, res, count;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                uni.showLoading({
                  title: '加载中'
                });
                _context4.prev = 1;
                location = {
                  longitude: '',
                  latitude: ''
                };
                _context4.prev = 3;
                _context4.next = 6;
                return new Promise(function (resolve) {
                  return setTimeout(resolve, 500);
                });
              case 6:
                _context4.next = 8;
                return new Promise(function (resolve, reject) {
                  uni.getLocation({
                    type: 'gcj02',
                    success: resolve,
                    fail: reject
                  });
                });
              case 8:
                location = _context4.sent;
                _this9.lng = location.longitude;
                _this9.lat = location.latitude;
                _context4.next = 18;
                break;
              case 13:
                _context4.prev = 13;
                _context4.t0 = _context4["catch"](3);
                _this9.lng = '115.259956';
                _this9.lat = '33.066271';
                uni.showToast({
                  icon: 'none',
                  title: '定位失败，使用默认位置'
                });
              case 18:
                _context4.prev = 18;
                _context4.next = 21;
                return new Promise(function (resolve, reject) {
                  uni.request({
                    url: "https://restapi.amap.com/v3/geocode/regeo?location=".concat(_this9.lng, ",").concat(_this9.lat, "&key=2fb9ec1a184338e3cce567b7d2bab08f"),
                    success: resolve,
                    fail: reject
                  });
                });
              case 21:
                geoRes = _context4.sent;
                _this9.province = geoRes.data.regeocode.addressComponent.province || '';
                _this9.city = geoRes.data.regeocode.addressComponent.city || '';
                _this9.district = geoRes.data.regeocode.addressComponent.district || '';
                _context4.next = 33;
                break;
              case 27:
                _context4.prev = 27;
                _context4.t1 = _context4["catch"](18);
                _this9.province = '安徽省';
                _this9.city = '阜阳市';
                _this9.district = '临泉县';
                uni.showToast({
                  icon: 'none',
                  title: '地址解析失败，使用默认地址'
                });
              case 33:
                _context4.next = 35;
                return _this9.$api.shifu.indexQuote({
                  lng: _this9.lng,
                  lat: _this9.lat,
                  parentId: 0,
                  pageNum: _this9.page,
                  pageSize: _this9.limit
                });
              case 35:
                res = _context4.sent;
                console.log(res);
                if (res.code === "-1") {
                  uni.showToast({
                    icon: 'none',
                    title: res.msg
                  }, 3000);
                }
                _this9.$set(_this9, 'list', res.data.list || []);
                count = _this9.list.length;
                console.log('List count:', count);
                uni.setStorageSync('listCount', count);
                _this9.$forceUpdate();
                _context4.next = 49;
                break;
              case 45:
                _context4.prev = 45;
                _context4.t2 = _context4["catch"](1);
                console.log(_context4.t2);
                // uni.showToast({
                // 	icon: 'none',
                // 	title: '获取订单列表失败'
                // });
                _this9.$set(_this9, 'list', []);
              case 49:
                _context4.prev = 49;
                uni.hideLoading();
                return _context4.finish(49);
              case 52:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[1, 45, 49, 52], [3, 13], [18, 27]]);
      }))();
    },
    handleReceive: function handleReceive(item) {
      this.textsss();
      this.orderData = item;
      this.id = item.id;
      if (item.type == 0) {
        this.confirmshow = true;
      } else {
        this.show = true;
      }
    },
    close: function close() {
      this.show = false;
      this.input = '';
    },
    confirmRe: function confirmRe() {
      var _this10 = this;
      this.confirmshow = false;
      this.$api.shifu.rece_Order({
        order_id: this.id
      }).then(function (res) {
        _this10.getList();
        uni.showToast({
          icon: 'success',
          title: '接单成功',
          duration: 1000
        });
        setTimeout(function () {
          uni.navigateTo({
            url: '/shifu/master_my_order'
          });
        }, 1000);
      }).catch(function (error) {
        uni.showToast({
          icon: 'fail',
          title: error.message || '接单失败'
        });
      });
    },
    goToSettle: function goToSettle() {
      this.masterModalShow = false;
      uni.navigateTo({
        url: '/shifu/Settle'
      });
    },
    getServiceInfo: function getServiceInfo() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var res;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                _context5.next = 3;
                return _this11.$api.shifu.index({
                  city_id: _this11.area_id
                });
              case 3:
                res = _context5.sent;
                _this11.bannerList = res.data || [];
                _this11.list1 = res.data.map(function (item) {
                  return item.img;
                }) || [];
                if (!_this11.list1.length) {
                  _this11.list1 = ['https://zskj.asia/attachment/image/666/24/09/2bdd13fab41b42b987bcfc501aa535bb.jpg'];
                }
                _context5.next = 13;
                break;
              case 9:
                _context5.prev = 9;
                _context5.t0 = _context5["catch"](0);
                uni.showToast({
                  icon: 'none',
                  title: '获取轮播图失败'
                });
                _this11.list1 = ['https://zskj.asia/attachment/image/666/24/09/e790eea3f21b4f48ab2b00b034468035.jpg'];
              case 13:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 9]]);
      }))();
    },
    onReachBottom: function onReachBottom() {
      var _this12 = this;
      if (this.status == 'nomore') return;
      this.status = 'loading';
      setTimeout(function () {
        _this12.page++;
        _this12.$api.shifu.indexQuote({
          pageNum: _this12.page,
          pageSize: 20,
          parentId: _this12.currentCateId || 0,
          lat: _this12.lat,
          lng: _this12.lng
        }).then(function (res) {
          if (!res.data || !res.data.list || res.data.list.length === 0) {
            _this12.status = 'nomore';
            uni.showToast({
              icon: 'none',
              title: '没有更多数据'
            });
            return;
          }
          _this12.$set(_this12, 'list', [].concat((0, _toConsumableArray2.default)(_this12.list), (0, _toConsumableArray2.default)(res.data.list || [])));
          if (res.data.list.length < 10) {
            _this12.status = 'nomore';
          } else {
            _this12.status = 'loadmore';
          }
        }).catch(function (error) {
          _this12.status = 'nomore';
          uni.showToast({
            icon: 'none',
            title: '加载失败，请稍后重试'
          });
        });
      }, 1000);
    },
    confirmBao: function confirmBao() {
      var _this13 = this;
      this.textsss();
      if (this.input == '' || this.input == 0) {
        uni.showToast({
          icon: 'none',
          title: '请输入报价（不能为0哦）'
        });
        return;
      }
      var updatedOrderData = {
        orderId: this.id,
        price: this.input
      };
      this.$api.shifu.updateBao(updatedOrderData).then(function (res) {
        if (res === -1) {
          _this13.masterModalShow = true;
        } else {
          uni.showToast({
            icon: 'success',
            title: '报价成功'
          });
          _this13.getList();
          _this13.close();
          setTimeout(function () {
            uni.redirectTo({
              url: '/shifu/master_bao_list'
            });
          }, 1000);
          _this13.getList();
        }
      }).catch(function (error) {
        uni.showToast({
          icon: 'fail',
          title: error.message || '报价失败'
        });
        _this13.close();
      });
    },
    initializePage: function initializePage() {
      var _this14 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var systemInfo;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                uni.showLoading({
                  title: '初始化中'
                });
                _context6.prev = 1;
                systemInfo = uni.getSystemInfoSync();
                console.log('Platform:', systemInfo.platform);
                _context6.next = 6;
                return _this14.getServiceInfo();
              case 6:
                _context6.next = 8;
                return _this14.getCate();
              case 8:
                _context6.next = 10;
                return _this14.getList();
              case 10:
                _context6.next = 12;
                return _this14.checkSubscriptionStatus();
              case 12:
                _this14.$forceUpdate();
                _context6.next = 18;
                break;
              case 15:
                _context6.prev = 15;
                _context6.t0 = _context6["catch"](1);
                uni.showToast({
                  icon: 'none',
                  title: '初始化失败，请稍后重试'
                });
              case 18:
                _context6.prev = 18;
                uni.hideLoading();
                return _context6.finish(18);
              case 21:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[1, 15, 18, 21]]);
      }))();
    }
  },
  onLoad: function onLoad() {
    var _this15 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
      return _regenerator.default.wrap(function _callee7$(_context7) {
        while (1) {
          switch (_context7.prev = _context7.next) {
            case 0:
              _this15.$api.base.getConfig().then(function (res) {
                // console.log(res)
                _this15.getconfigs = res.shifuQualityCommitment;
                // console.log(this.getconfigs)
              });
              // Modified assignment for shifustutus
              _this15.$api.shifu.getshifstutas().then(function (res) {
                console.log("shifustutus response:", res);
                _this15.shifustutus = res; // Assign the whole response object
              });

              if (uni.getStorageSync('shiInfo')) {
                _this15.infodata = JSON.parse(uni.getStorageSync('shiInfo'));
              }
              _this15.configInfo = uni.getStorageSync('configInfo');
              console.log(_this15.infodata);
              _this15.isPageLoaded = true; // 标记页面已初始化
              _context7.next = 8;
              return _this15.initializePage();
            case 8:
            case "end":
              return _context7.stop();
          }
        }
      }, _callee7);
    }))();
  },
  onPullDownRefresh: function onPullDownRefresh() {
    var _this16 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
      return _regenerator.default.wrap(function _callee8$(_context8) {
        while (1) {
          switch (_context8.prev = _context8.next) {
            case 0:
              _this16.page = 1;
              _this16.list = [];
              _context8.next = 4;
              return _this16.getList();
            case 4:
              uni.stopPullDownRefresh();
            case 5:
            case "end":
              return _context8.stop();
          }
        }
      }, _callee8);
    }))();
  },
  onShow: function onShow() {
    var _this17 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
      return _regenerator.default.wrap(function _callee9$(_context9) {
        while (1) {
          switch (_context9.prev = _context9.next) {
            case 0:
              _this17.checkSubscriptionStatus();
              uni.$on('refreshReceivingList', function () {
                console.log('接收到刷新通知，正在重新加载订单列表...');
                // 这里可以加上一些交互提示，比如显示一个 loading
                // 然后立即调用获取数据的方法
                _this17.page = 1;
                _this17.getList();
              });
            case 2:
            case "end":
              return _context9.stop();
          }
        }
      }, _callee9);
    }))();
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 633:
/*!******************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?vue&type=style&index=0&id=23563916&scoped=true&lang=scss& ***!
  \******************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_style_index_0_id_23563916_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Receiving.vue?vue&type=style&index=0&id=23563916&scoped=true&lang=scss& */ 634);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_style_index_0_id_23563916_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_style_index_0_id_23563916_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_style_index_0_id_23563916_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_style_index_0_id_23563916_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_Receiving_vue_vue_type_style_index_0_id_23563916_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 634:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?vue&type=style&index=0&id=23563916&scoped=true&lang=scss& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[627,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/shifu/Receiving.js.map