<template>
	<view class="page-container">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="header-title">师傅服务中心</view>
		</view>

		<!-- 二级滑动容器 -->
		<view class="swiper-container">
			<!-- 一级滑动 - 主要功能区域 -->
			<swiper
				class="main-swiper"
				:current="currentMainIndex"
				@change="onMainSwiperChange"
				:indicator-dots="true"
				:indicator-color="'rgba(255,255,255,0.3)'"
				:indicator-active-color="'#007AFF'"
			>
				<!-- 第一页：服务选择 -->
				<swiper-item>
					<view class="swiper-page service-page">
						<view class="page-title">请选择服务类目</view>
						<view class="service-list">
							<view class="service-card install-card" @tap="selectService(21, '安装服务')">
								<view class="card-content">
									<view class="service-text">上门安装</view>
									<view class="service-icon">
										<view class="icon-placeholder install-icon">🔧</view>
									</view>
								</view>
							</view>
							<view class="service-card repair-card" @tap="selectService(22, '维修服务')">
								<view class="card-content">
									<view class="service-text">上门维修</view>
									<view class="service-icon">
										<image
											src="https://zskj.asia/attachment/image/666/25/05/86fe36a3384346ef8e31bcc6b06f0c43.jpg"
											mode="aspectFit"
											class="icon-image"
										></image>
									</view>
								</view>
							</view>
							<view class="service-card clean-card" @tap="selectService(23, '清洗服务')">
								<view class="card-content">
									<view class="service-text">商用办公</view>
									<view class="service-icon">
										<image
											src="https://zskj.asia/attachment/image/666/25/05/9a10d8df55c64370893ae861d2ff9887.jpg"
											mode="aspectFit"
											class="icon-image"
										></image>
									</view>
								</view>
							</view>
							<view class="service-card rescue-card" @tap="selectService(46, '出行救援')">
								<view class="card-content">
									<view class="service-text">家政保洁</view>
									<view class="service-icon">
										<image
											src="https://zskj.asia/attachment/image/666/25/05/8df2da0eaeee41828c4535f6145a375c.jpg"
											mode="aspectFit"
											class="icon-image"
										></image>
									</view>
								</view>
							</view>
						</view>
					</view>
				</swiper-item>

				<!-- 第二页：信用信息 -->
				<swiper-item>
					<view class="swiper-page credit-page">
						<view class="page-title">师傅信用信息</view>
						<view class="credit-content">
							<!-- 信用评分卡片 -->
							<view class="credit-card">
								<view class="credit-header">
									<view class="credit-title">信用评分</view>
									<view class="credit-score">{{ creditInfo.score }}</view>
								</view>
								<view class="credit-level">{{ creditInfo.level }}</view>
								<view class="credit-progress">
									<view class="progress-bar">
										<view class="progress-fill" :style="{ width: creditInfo.score + '%' }"></view>
									</view>
								</view>
							</view>

							<!-- 信用详情 -->
							<view class="credit-details">
								<view class="detail-item">
									<view class="detail-label">服务次数</view>
									<view class="detail-value">{{ creditInfo.serviceCount }}次</view>
								</view>
								<view class="detail-item">
									<view class="detail-label">好评率</view>
									<view class="detail-value">{{ creditInfo.goodRate }}%</view>
								</view>
								<view class="detail-item">
									<view class="detail-label">响应时间</view>
									<view class="detail-value">{{ creditInfo.responseTime }}分钟</view>
								</view>
								<view class="detail-item">
									<view class="detail-label">完成率</view>
									<view class="detail-value">{{ creditInfo.completionRate }}%</view>
								</view>
							</view>

							<!-- 二级滑动 - 信用详细信息 -->
							<view class="sub-swiper-container">
								<swiper
									class="sub-swiper"
									:current="currentSubIndex"
									@change="onSubSwiperChange"
									:indicator-dots="true"
									:indicator-color="'rgba(0,0,0,0.3)'"
									:indicator-active-color="'#007AFF'"
								>
									<!-- 评价记录 -->
									<swiper-item>
										<view class="sub-page">
											<view class="sub-title">最近评价</view>
											<view class="review-list">
												<view class="review-item" v-for="(review, index) in creditInfo.reviews" :key="index">
													<view class="review-header">
														<view class="review-user">{{ review.userName }}</view>
														<view class="review-date">{{ review.date }}</view>
													</view>
													<view class="review-rating">
														<text class="star" v-for="i in 5" :key="i" :class="{ active: i <= review.rating }">★</text>
													</view>
													<view class="review-content">{{ review.content }}</view>
												</view>
											</view>
										</view>
									</swiper-item>

									<!-- 服务记录 -->
									<swiper-item>
										<view class="sub-page">
											<view class="sub-title">服务记录</view>
											<view class="service-record-list">
												<view class="record-item" v-for="(record, index) in creditInfo.serviceRecords" :key="index">
													<view class="record-header">
														<view class="record-service">{{ record.serviceName }}</view>
														<view class="record-status" :class="record.status">{{ record.statusText }}</view>
													</view>
													<view class="record-time">{{ record.time }}</view>
													<view class="record-address">{{ record.address }}</view>
												</view>
											</view>
										</view>
									</swiper-item>
								</swiper>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 选择器组件 -->
		<u-picker
			:show="showCategoryPicker"
			:columns="categoryColumns"
			@cancel="showCategoryPicker = false"
			@confirm="confirmCategory"
			keyName="name"
		></u-picker>

		<u-picker
			:show="showServicePicker"
			:columns="serviceColumns"
			@cancel="showServicePicker = false"
			@confirm="confirmService"
			keyName="title"
		></u-picker>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 主滑动索引
				currentMainIndex: 0,
				// 子滑动索引
				currentSubIndex: 0,

				// 服务相关数据
				showCategoryPicker: false,
				showServicePicker: false,
				categoryColumns: [[]],
				serviceColumns: [[]],
				selectedCategoryData: null,
				selectedServiceData: null,
				currentServiceId: null,
				currentServiceName: '',

				// 信用信息数据
				creditInfo: {
					score: 85,
					level: '优秀师傅',
					serviceCount: 156,
					goodRate: 98.5,
					responseTime: 15,
					completionRate: 99.2,
					reviews: [
						{
							userName: '张先生',
							date: '2024-01-15',
							rating: 5,
							content: '师傅很专业，服务态度好，维修质量高！'
						},
						{
							userName: '李女士',
							date: '2024-01-12',
							rating: 5,
							content: '响应速度快，技术过硬，值得推荐！'
						},
						{
							userName: '王先生',
							date: '2024-01-10',
							rating: 4,
							content: '服务不错，就是来得稍微晚了一点。'
						}
					],
					serviceRecords: [
						{
							serviceName: '空调维修',
							status: 'completed',
							statusText: '已完成',
							time: '2024-01-15 14:30',
							address: '朝阳区建国路88号'
						},
						{
							serviceName: '洗衣机安装',
							status: 'completed',
							statusText: '已完成',
							time: '2024-01-12 10:15',
							address: '海淀区中关村大街1号'
						},
						{
							serviceName: '热水器维修',
							status: 'in-progress',
							statusText: '进行中',
							time: '2024-01-16 09:00',
							address: '西城区西单北大街120号'
						}
					]
				}
			}
		},
		methods: {
			// 主滑动切换
			onMainSwiperChange(e) {
				this.currentMainIndex = e.detail.current;
			},

			// 子滑动切换
			onSubSwiperChange(e) {
				this.currentSubIndex = e.detail.current;
			},

			// 选择服务（从fast.vue复制的逻辑）
			async selectService(serviceId, serviceName) {
				console.log('选择服务:', serviceId, serviceName);

				this.currentServiceId = serviceId;
				this.currentServiceName = serviceName;

				try {
					// 获取城市位置信息
					const city = uni.getStorageSync('city') || { position: '' };
					console.log('城市信息:', city);

					// 调用API获取服务分类数据
					const response = await this.$api.service.setserviceCate(city.position);
					console.log('API响应:', response);

					if (response && response.code === "200" && response.data) {
						// 查找对应的服务分类
						const selectedCategory = response.data.find(item => item.id === serviceId);
						console.log('找到的分类:', selectedCategory);

						if (selectedCategory && selectedCategory.children && selectedCategory.children.length > 0) {
							// 设置分类选择器数据
							this.categoryColumns = [selectedCategory.children];

							console.log('设置分类选择器数据:', this.categoryColumns);

							// 显示分类选择器
							this.showCategoryPicker = true;
						} else {
							console.log('没有子分类或子分类为空');
							uni.showToast({
								title: '该服务暂无子分类',
								icon: 'none'
							});
						}
					} else {
						console.log('API响应格式错误:', response);
						uni.showToast({
							title: '获取服务数据失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取服务数据失败:', error);
					uni.showToast({
						title: '网络错误，请重试: ' + error.message,
						icon: 'none'
					});
				}
			},

			// 确认分类选择
			confirmCategory(e) {
				console.log('选择的分类:', e);
				this.selectedCategoryData = e.value[0];

				if (this.selectedCategoryData && this.selectedCategoryData.serviceList && this.selectedCategoryData.serviceList.length > 0) {
					// 设置服务选择器数据
					this.serviceColumns = [this.selectedCategoryData.serviceList];

					// 隐藏分类选择器并显示服务选择器
					this.showCategoryPicker = false;
					this.showServicePicker = true;
				} else {
					uni.showToast({
						title: '该分类下暂无服务',
						icon: 'none'
					});
				}
			},

			// 确认服务选择
			confirmService(e) {
				console.log('选择的服务:', e);
				this.selectedServiceData = e.value[0];

				if (this.selectedServiceData) {
					// 隐藏服务选择器
					this.showServicePicker = false;
					// 跳转到详情页
					this.goToDetails(this.selectedServiceData.id);
				}
			},

			// 跳转到详情页
			goToDetails(id) {
				uni.navigateTo({
					url: `../user/commodity_details?id=${id}`,
					fail: (err) => {
						console.error("Navigation failed:", err);
						uni.showToast({
							title: "跳转失败: " + err.errMsg,
							icon: "none",
						});
					},
				});
			},

			// 关闭选择器
			closePickers() {
				this.showCategoryPicker = false;
				this.showServicePicker = false;
			}
		}
	}
</script>
