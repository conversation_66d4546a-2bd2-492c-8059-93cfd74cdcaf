{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-checkbox-group/u-checkbox-group.vue?ac02", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-checkbox-group/u-checkbox-group.vue?63a6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-checkbox-group/u-checkbox-group.vue?4adf", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-checkbox-group/u-checkbox-group.vue?297a", "uni-app:///node_modules/uview-ui/components/u-checkbox-group/u-checkbox-group.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-checkbox-group/u-checkbox-group.vue?002f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-checkbox-group/u-checkbox-group.vue?8b77"], "names": ["name", "mixins", "computed", "parentData", "bemClass", "watch", "data", "created", "methods", "unCheckedOther", "values"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+1B,CAAgB,+2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACUn3B;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,eAuBA;EACAA;EACAC;EACAC;IACA;IACA;IACA;IACAC;MACA,oHACA,iDACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAF;MACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAG;IACA,QAEA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;QACA;UACAC;QACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAAsmD,CAAgB,0jDAAG,EAAC,C;;;;;;;;;;;ACA1nD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-checkbox-group/u-checkbox-group.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-checkbox-group.vue?vue&type=template&id=3d1b15f2&scoped=true&\"\nvar renderjs\nimport script from \"./u-checkbox-group.vue?vue&type=script&lang=js&\"\nexport * from \"./u-checkbox-group.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-checkbox-group.vue?vue&type=style&index=0&id=3d1b15f2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3d1b15f2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-checkbox-group/u-checkbox-group.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-checkbox-group.vue?vue&type=template&id=3d1b15f2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-checkbox-group.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-checkbox-group.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t    class=\"u-checkbox-group\"\n\t    :class=\"bemClass\"\n\t>\n\t\t<slot></slot>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * checkboxGroup 复选框组\n\t * @description 复选框组件一般用于需要多个选择的场景，该组件功能完整，使用方便\n\t * @tutorial https://www.uviewui.com/components/checkbox.html\n\t * @property {String}\t\t\tname\t\t\t标识符 \n\t * @property {Array}\t\t\tvalue\t\t\t绑定的值\n\t * @property {String}\t\t\tshape\t\t\t形状，circle-圆形，square-方形 （默认 'square' ）\n\t * @property {Boolean}\t\t\tdisabled\t\t是否禁用全部checkbox （默认 false ）\n\t * @property {String}\t\t\tactiveColor\t\t选中状态下的颜色，如设置此值，将会覆盖parent的activeColor值 （默认 '#2979ff' ）\n\t * @property {String}\t\t\tinactiveColor\t未选中的颜色 （默认 '#c8c9cc' ）\n\t * @property {String | Number}\tsize\t\t\t整个组件的尺寸 单位px （默认 18 ）\n\t * @property {String}\t\t\tplacement\t\t布局方式，row-横向，column-纵向 （默认 'row' ）\n\t * @property {String | Number}\tlabelSize\t\tlabel的字体大小，px单位  （默认 14 ）\n\t * @property {String}\t\t\tlabelColor\t\tlabel的字体颜色 （默认 '#303133' ）\n\t * @property {Boolean}\t\t\tlabelDisabled\t是否禁止点击文本操作 (默认 false )\n\t * @property {String}\t\t\ticonColor\t\t图标颜色 （默认 '#ffffff' ）\n\t * @property {String | Number}\ticonSize\t\t图标的大小，单位px （默认 12 ）\n\t * @property {String}\t\t\ticonPlacement\t勾选图标的对齐方式，left-左边，right-右边  （默认 'left' ）\n\t * @property {Boolean}\t\t\tborderBottom\tplacement为row时，是否显示下边框 （默认 false ）\n\t * @event {Function}\tchange\t任一个checkbox状态发生变化时触发，回调为一个对象\n\t * @event {Function}\tinput\t修改通过v-model绑定的值时触发，回调为一个对象\n\t * @example <u-checkbox-group></u-checkbox-group>\n\t */\n\texport default {\n\t\tname: 'u-checkbox-group',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tcomputed: {\n\t\t\t// 这里computed的变量，都是子组件u-checkbox需要用到的，由于头条小程序的兼容性差异，子组件无法实时监听父组件参数的变化\n\t\t\t// 所以需要手动通知子组件，这里返回一个parentData变量，供watch监听，在其中去通知每一个子组件重新从父组件(u-checkbox-group)\n\t\t\t// 拉取父组件新的变化后的参数\n\t\t\tparentData() {\n\t\t\t\treturn [this.value, this.disabled, this.inactiveColor, this.activeColor, this.size, this.labelDisabled, this.shape,\n\t\t\t\t\tthis.iconSize, this.borderBottom, this.placement\n\t\t\t\t]\n\t\t\t},\n\t\t\tbemClass() {\n\t\t\t\t// this.bem为一个computed变量，在mixin中\n\t\t\t\treturn this.bem('checkbox-group', ['placement'])\n\t\t\t},\n\t\t},\n\t\twatch: {\n\t\t\t// 当父组件需要子组件需要共享的参数发生了变化，手动通知子组件\n\t\t\tparentData() {\n\t\t\t\tif (this.children.length) {\n\t\t\t\t\tthis.children.map(child => {\n\t\t\t\t\t\t// 判断子组件(u-checkbox)如果有init方法的话，就就执行(执行的结果是子组件重新从父组件拉取了最新的值)\n\t\t\t\t\t\ttypeof(child.init) === 'function' && child.init()\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.children = []\n\t\t},\n\t\tmethods: {\n\t\t\t// 将其他的checkbox设置为未选中的状态\n\t\t\tunCheckedOther(childInstance) {\n\t\t\t\tconst values = []\n\t\t\t\tthis.children.map(child => {\n\t\t\t\t\t// 将被选中的checkbox，放到数组中返回\n\t\t\t\t\tif (child.isChecked) {\n\t\t\t\t\t\tvalues.push(child.name)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t// 发出事件\n\t\t\t\tthis.$emit('change', values)\n\t\t\t\t// 修改通过v-model绑定的值\n\t\t\t\tthis.$emit('input', values)\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-checkbox-group {\n\n\t\t&--row {\n\t\t\t@include flex;\n\t\t}\n\n\t\t&--column {\n\t\t\t@include flex(column);\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-checkbox-group.vue?vue&type=style&index=0&id=3d1b15f2&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-checkbox-group.vue?vue&type=style&index=0&id=3d1b15f2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755741550435\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}