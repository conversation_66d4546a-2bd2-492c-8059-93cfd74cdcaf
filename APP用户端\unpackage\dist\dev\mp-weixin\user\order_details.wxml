<view class="page data-v-f98852ca"><u-modal vue-id="3781046a-1" show="{{show}}" title="取消订单" content="确认取消该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e0']]],['^confirm',[['confirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-f98852ca" bind:__l="__l"></u-modal><u-modal vue-id="3781046a-2" show="{{afterSalesShow}}" title="申请售后" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e1']]],['^confirm',[['submitAfterSales']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-f98852ca" bind:__l="__l" vue-slots="{{['default']}}"><view class="after-sales-input data-v-f98852ca"><view class="data-v-f98852ca"><textarea style="padding:20rpx;border:2rpx solid #E9E9E9;border-radius:8rpx;writing-mode:horizontal-tb;text-align:left;" placeholder="请输入售后内容" data-event-opts="{{[['input',[['__set_model',['','afterSalesValue','$event',[]]]]]]}}" value="{{afterSalesValue}}" bindinput="__e" class="data-v-f98852ca"></textarea></view></view></u-modal><block wx:if="{{info.coachInfo}}"><view class="header data-v-f98852ca"><view class="top data-v-f98852ca"><view class="left data-v-f98852ca"><view style="display:flex;align-items:center;" class="data-v-f98852ca"><view class="name data-v-f98852ca">{{info.coachInfo.coachName}}</view><block wx:if="{{info.coachInfo.label_name}}"><view style="background-color:#fac21f;color:#fff;width:fit-content;padding:5rpx 10rpx;font-size:24rpx;margin-left:20rpx;border-radius:6rpx;" class="data-v-f98852ca">{{info.coachInfo.label_name}}</view></block></view><view class="time data-v-f98852ca">{{info.createTime}}</view></view><view class="right data-v-f98852ca"><image src="{{info.coachInfo.selfImg}}" mode class="data-v-f98852ca"></image></view></view><block wx:if="{{!$root.g0}}"><view data-event-opts="{{[['tap',[['call',['$event']]]]]}}" class="bott data-v-f98852ca" bindtap="__e"><view class="box data-v-f98852ca"><uni-icons vue-id="3781046a-3" type="phone-filled" size="16" color="#fff" class="data-v-f98852ca" bind:__l="__l"></uni-icons></view><text class="data-v-f98852ca">打电话给师傅</text></view></block></view></block><block wx:if="{{info.payType===7}}"><view data-event-opts="{{[['tap',[['openAfterSales',['$event']]]]]}}" class="after-sales-btn data-v-f98852ca" bindtap="__e">去售后</view></block><block wx:if="{{info.payType===7&&info.warrantyType===1}}"><view data-event-opts="{{[['tap',[['viewWarranty',['$event']]]]]}}" class="after-sales-btn data-v-f98852ca" bindtap="__e">查看质保单</view></block><block wx:else><block wx:if="{{info.payType===7}}"><view data-event-opts="{{[['tap',[['opengenerate',['$event']]]]]}}" class="after-sales-btn data-v-f98852ca" bindtap="__e">生成质保单</view></block></block><block wx:if="{{info.payType!==7}}"><view class="schedule data-v-f98852ca"><u-steps vue-id="3781046a-4" current="4" direction="column" class="data-v-f98852ca" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-steps-item vue-id="{{('3781046a-5-'+index)+','+('3781046a-4')}}" title="{{item.title}}" desc="{{item.desc}}" class="data-v-f98852ca" bind:__l="__l" vue-slots="{{['icon']}}"><view class="slot-icon data-v-f98852ca" slot="icon"><view style="border-radius:50%;background-color:#00b26a;padding:5rpx;" class="data-v-f98852ca"><u-icon vue-id="{{('3781046a-6-'+index)+','+('3781046a-5-'+index)}}" name="checkbox-mark" color="#ffffff" size="14" class="data-v-f98852ca" bind:__l="__l"></u-icon></view></view></u-steps-item></block></u-steps></view></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info data-v-f98852ca"><view class="title data-v-f98852ca">{{item.$orig.title}}</view><block wx:if="{{item.$orig.title==='服务信息'}}"><view class="service-info data-v-f98852ca"><block wx:for="{{item.l0}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view class="info_item data-v-f98852ca"><view class="left data-v-f98852ca">{{newItem.name}}</view><view class="right data-v-f98852ca">{{newItem.value}}</view></view></block><view class="goods-list data-v-f98852ca"><block wx:for="{{info.orderGoods}}" wx:for-item="goods" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods-card data-v-f98852ca"><view class="goods-main data-v-f98852ca"><image class="goods-image data-v-f98852ca" src="{{goods.goodsCover}}" mode="aspectFill"></image><view class="goods-content data-v-f98852ca"><view class="goods-name data-v-f98852ca">{{goods.goodsName}}</view><view class="goods-details data-v-f98852ca"><block wx:for="{{goods.priceSetting}}" wx:for-item="setting" wx:for-index="__i0__" wx:key="id"><view class="detail-item data-v-f98852ca"><block wx:if="{{setting.val&&setting.val!==''&&setting.inputType!==2}}"><text class="detail-text data-v-f98852ca">{{setting.problemDesc+"："+setting.val}}</text></block></view></block></view></view><view class="goods-right data-v-f98852ca"><block wx:if="{{goods.price>0}}"><view class="goods-price data-v-f98852ca">{{"¥"+goods.price}}</view></block><view class="goods-num data-v-f98852ca">{{"x"+goods.num}}</view></view></view></view></block></view></view></block><block wx:else><block wx:for="{{item.$orig.children}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view class="info_item data-v-f98852ca"><view class="left data-v-f98852ca">{{newItem.name}}</view><view class="right data-v-f98852ca">{{newItem.value}}</view></view></block></block></view></block><block wx:if="{{info.payType<=1&&info.payType!=-1}}"><view data-event-opts="{{[['tap',[['cancelOrder',['$event']]]]]}}" class="btn data-v-f98852ca" bindtap="__e">取消订单</view></block></view>